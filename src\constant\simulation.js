
export const modulationTypeOptions = [
  {
    label: 'PSK', value: 'PSK', children: [
      { label: 'BPSK', value: 'BPSK' },
      { label: 'π/2 BPSK', value: 'π/2 BPSK' },
      { label: 'QPSK', value: 'QPSK' },
      { label: 'QPSK(EDGE)', value: 'QPSK(EDGE)' },
      { label: 'AQPSK', value: 'AQPSK' },
      { label: 'π/4 QPSK', value: 'π/4 QPSK' },
      { label: 'π/2 QPSK', value: 'π/2 QPSK' },
      { label: 'OQPSK', value: 'OQPSK' },
      { label: 'SOQPSK', value: 'SOQPSK' },
      { label: 'UQPSK', value: 'UQPSK' },
      { label: '8-PSK', value: '8-PSK' },
      { label: '8-PSK(EDGE)', value: '8-PSK(EDGE)' },
      { label: 'π/2 8-PSK', value: 'π/2 8-PS<PERSON>' },
      { label: 'π3/8 8-PSK', value: 'π3/8 8-PSK' },
      { label: 'O-8PSK', value: 'O-8PSK' },
      { label: '16-<PERSON><PERSON>', value: '16-PSK' },
    ]
  },
  {
    label: 'DPSK', value: 'DPSK', children: [
      { label: 'π/2 DBPSK', value: 'π/2 DBPSK' },
      { label: 'π/4 DBPSK', value: 'π/4 DBPSK' },
      { label: 'π/8 DBPSK', value: 'π/8 DBPSK' },
      { label: 'π/4 DQPSK', value: 'π/4 DQPSK' },
      { label: 'π/8 DQPSK', value: 'π/8 DQPSK' },
      { label: 'SDPSK', value: 'SDPSK' },
      { label: 'DQPSK', value: 'DQPSK' },
      { label: 'D8PSK', value: 'D8PSK' },
      { label: 'D16PSK', value: 'D16PSK' },
    ]
  },
  {
    label: 'QAM', value: 'QAM', children: [
      { label: '4-QAM', value: '4-QAM' },
      { label: '8-QAM', value: '8-QAM' },
      { label: '16-QAM', value: '16-QAM' },
      { label: '16-QAM(EDGE)', value: '16-QAM(EDGE)' },
      { label: '32-QAM', value: '32-QAM' },
      { label: '32-QAM(EDGE)', value: '32-QAM(EDGE)' },
      { label: '64-QAM', value: '64-QAM' },
      { label: '128-QAM', value: '128-QAM' },
      { label: '256-QAM', value: '256-QAM' },
      { label: '512-QAM', value: '512-QAM' },
      { label: '1024-QAM', value: '1024-QAM' },
      { label: '2048-QAM', value: '2048-QAM' },
      { label: '4096-QAM', value: '4096-QAM' },
      { label: '8192-QAM', value: '8192-QAM' },
      { label: '16384-QAM', value: '16384-QAM' },
    ]
  },
  {
    label: 'MSK', value: 'MSK', children: [
      { label: 'MSK', value: 'MSK' },
      { label: 'GMSK', value: 'GMSK' },
      { label: 'CPM', value: 'CPM' },
    ]
  },
  {
    label: 'FSK', value: 'FSK', children: [
      { label: '2-FSK', value: '2-FSK' },
      { label: '4-FSK', value: '4-FSK' },
      { label: '8-FSK', value: '8-FSK' },
      { label: '16-FSK', value: '16-FSK' },
      { label: '32-FSK', value: '32-FSK' },
      { label: '64-FSK', value: '64-FSK' },
    ]
  },
  {
    label: 'PAM', value: 'PAM', children: [
      { label: 'PAM4', value: 'PAM4' },
      { label: 'PAM8', value: 'PAM8' },
      { label: 'PAM16', value: 'PAM16' },

    ]
  },
  {
    label: 'ASK', value: 'ASK', children: [
      { label: 'ASK', value: 'ASK' },
      { label: 'OOK', value: 'OOK' },

    ]
  },
  {
    label: 'APSK', value: 'APSK', children: [
      { label: '16APSK', value: '16APSK' },
      { label: '32APSK', value: '32APSK' },
      { label: '64APSK', value: '64APSK' },
      { label: '128APSK', value: '128APSK' },
      { label: '256APSK', value: '256APSK' },
    ]
  },
]


