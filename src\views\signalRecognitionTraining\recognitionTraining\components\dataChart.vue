<template>
  <div>
    <div class="flex items-center gap-5 ml-5">
      <el-select
        v-model="selectedFreStr"
        :teleported="false"
        size="small"
        multiple
        collapse-tags
        collapse-tags-tooltip
        value-key="label"
      >
        <el-option
          v-for="item in frequencyStandards"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </el-select>
      <el-checkbox v-model="isTopFollow" label="峰值跟踪" border />
    </div>
    <!-- 信号检测echarts -->
    <Spectrum
      ref="spectrumRef"
      :id="id"
      :selected-markers="selectedFreStr"
      :is-top-follow="isTopFollow"
      :data="data"
    />
  </div>
</template>

<script setup>
  import Spectrum from './Spectrum.vue'

  const props = defineProps({
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => []
    }
  })

  const frequencyStandards = [
    { label: '频标1', value: 1 },
    { label: '频标2', value: 2 },
    { label: '频标3', value: 3 },
    { label: '频标4', value: 4 },
    { label: '频标5', value: 5 }
  ]

  const spectrumRef = ref(null)
  const selectedFreStr = ref([])
  const isTopFollow = ref(false)
</script>

<style scoped>
  :deep(.el-input__inner) {
    color: #ffffff;
  }
</style>
