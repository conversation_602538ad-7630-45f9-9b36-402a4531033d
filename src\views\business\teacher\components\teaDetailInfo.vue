<script setup>
  import { getTeacher } from '@/api/business/teacher'
  import useDictStore from '@/store/modules/dict'
  import pattern from '@/utils/pattern'

  // 定义 emits
  const emit = defineEmits(['submit'])
  const props = defineProps({
    id: {
      type: String,
      default: null
    }
  })

  const dictStore = useDictStore()
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const teaDetailFormRef = ref(null)
  const sysUserSexOptions = ref([]) // 性别字典数据
  const teaMajorOptions = ref([]) // 专业字典数据
  const teachingEducationOptions = ref([]) // 学历字典数据
  const teachingPoliticalOptions = ref([]) // 政治面貌字典数据
  const form = ref({})
  const loading = ref(false)

  // 表单校验规则
  const teaDetailFormRules = ref({
    teaCode: [{ required: true, message: '请输入教员编号', trigger: 'blur' }],
    teaName: [{ required: true, message: '请输入教员姓名', trigger: 'blur' }],
    teaSex: [{ required: true, message: '请选择性别', trigger: 'change' }],
    teaPhone: [
      { message: '请输入手机号码', trigger: 'blur' },
      { pattern: pattern.TEL, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    teaIdent: [
      { message: '请输入身份证', trigger: 'blur' },
      { pattern: pattern.IDENT, message: '请输入正确的身份证', trigger: 'blur' }
    ],
    teaNative: [
      { message: '请输入籍贯', trigger: 'blur' },
      { pattern: pattern.STRING, message: '请输入正确的籍贯', trigger: 'blur' }
    ],
    teaEmail: [
      { message: '请输入邮箱', trigger: 'blur' },
      { pattern: pattern.EMAIL, message: '请输入正确的邮箱', trigger: 'blur' }
    ]
  })

  const getInfo = async newId => {
    if (!newId) return
    loading.value = true
    try {
      const res = await getTeacher(newId)
      form.value = res.data
    } catch (error) {
      console.error('获取教员信息失败', error)
    } finally {
      loading.value = false
    }
  }

  const handleClose = () => {
    dialogTableVisible.value = false
  }

  const confirm = () => {
    teaDetailFormRef.value?.validate(valid => {
      if (!valid) return
      emit('submit', form.value)
    })
  }

  const getDictionaryData = async () => {
    const processDictionary = (dictType, targetOptions) => {
      dictStore.dict
        .filter(item => item.dictType === dictType)
        .forEach(item => {
          targetOptions.value.push({ label: item.dictLabel, value: item.dictValue })
        })
    }
    processDictionary('sys_user_sex', sysUserSexOptions)
    processDictionary('stu_major', teaMajorOptions)
    processDictionary('teaching_education', teachingEducationOptions)
    processDictionary('teaching_political', teachingPoliticalOptions)
  }

  onMounted(async () => {
    await getDictionaryData()
    await getInfo(props.id)
  })

  defineExpose({
    teaDetailFormRules
  })
</script>

<template>
  <el-dialog
    class="px-10"
    v-model="dialogTableVisible"
    :title="id ? '编辑教员' : '新增教员'"
    align="center"
    width="960px"
    @closed="handleClose"
  >
    <el-form
      v-loading="loading"
      ref="teaDetailFormRef"
      :model="form"
      :rules="teaDetailFormRules"
      label-width="auto"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="教员编号：" prop="teaCode">
            <el-input v-model="form.teaCode" placeholder="请输入教员学号"></el-input>
          </el-form-item>
          <el-form-item label="教员性别：" prop="teaSex">
            <el-select v-model="form.teaSex" placeholder="请选择教员性别（必填）">
              <el-option
                v-for="item in sysUserSexOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="身份证：" prop="teaIdent">
            <el-input
              v-model="form.teaIdent"
              :maxlength="18"
              placeholder="请输入身份证："
            ></el-input>
          </el-form-item>

          <el-form-item label="出生日期：" prop="teaBirth">
            <vxe-input
              style="width: 100%"
              v-model="form.teaBirth"
              type="date"
              placeholder="请选择出生日期"
              class="time-button"
              clearable
            />
          </el-form-item>
          <el-form-item label="专业：" prop="teaMajor">
            <el-select v-model="form.teaMajor" placeholder="请选择专业">
              <el-option
                v-for="item in teaMajorOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="入职时间：" prop="teaIndate">
            <vxe-input
              style="width: 100%"
              v-model="form.teaIndate"
              type="date"
              placeholder="请选择入学时间"
              class="time-button"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="教员姓名：" prop="teaName">
            <el-input v-model="form.teaName" placeholder="请输入姓名"></el-input>
          </el-form-item>
          <el-form-item label="手机号码：" prop="teaPhone">
            <el-input
              v-model="form.teaPhone"
              :maxlength="11"
              placeholder="请输入手机号码："
            ></el-input>
          </el-form-item>
          <el-form-item label="籍贯：" prop="teaNative">
            <el-input v-model="form.teaNative" placeholder="请输入籍贯："></el-input>
          </el-form-item>
          <el-form-item label="用户邮箱：" prop="teaEmail">
            <el-input v-model="form.teaEmail" placeholder="请输入用户邮箱："></el-input>
          </el-form-item>
          <el-form-item label="学历：" prop="teaEducation">
            <el-select v-model="form.teaEducation" placeholder="请选择学历">
              <el-option
                v-for="item in teachingEducationOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="政治面貌：" prop="teaPolitical">
            <el-select v-model="form.teaPolitical" placeholder="请选择政治面貌">
              <el-option
                v-for="item in teachingPoliticalOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="text-center">
        <vxe-button @click="handleClose">取消</vxe-button>
        <vxe-button status="primary" @click="confirm">确认</vxe-button>
      </div>
    </template>
  </el-dialog>
</template>

<style></style>
