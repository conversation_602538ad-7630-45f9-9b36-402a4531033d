<script setup>
  import * as echarts from 'echarts'
  import useDictStore from '@/store/modules/dict'

  // 定义 props 接收父组件数据
  const props = defineProps({
    reportData: {
      type: Object,
      default: () => ({})
    }
  })

  // 对话框状态
  const dialogVisible = defineModel({ type: Boolean, default: false })
  const dictStore = useDictStore()
  const chartType = ref('chart') // 修改默认值为 'chart'
  const chartContainer = ref(null) // 使用 ref 绑定图表容器 DOM
  let chartInstance = null // 图表实例

  // 将字典数据转换为计算属性，自动过滤出 train_status 相关项
  const trainStatusOptions = computed(() => {
    return dictStore.dict
      .filter(item => item.dictType === 'train_status')
      .map(item => ({ label: item.dictLabel, value: item.dictValue }))
  })

  // 将训练状态的 label 映射封装为函数
  const getStatusLabel = value => {
    const found = trainStatusOptions.value.find(opt => opt.value === value)
    return found ? found.label : '-'
  }

  const chartOption = computed(() => {
    // 获取各项指标数据，若不存在则默认 0
    const totalNum = props.reportData.totalNum || 0
    const unTrainedNum = props.reportData.unTrainedNum || 0
    const trainedNum = props.reportData.trainedNum || 0
    const correctNum = props.reportData.correctNum || 0
    const rankedNum = props.reportData.rankedNum || 0

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      xAxis: {
        type: 'category',
        data: ['应训人数', '未训练', '已训练', '已评分', '缺训'],
        axisTick: { alignWithLabel: true }
      },
      yAxis: {
        type: 'value',
        axisLabel: { formatter: '{value} 人' },
        interval: 1,
        minInterval: 1
      },
      series: [
        {
          name: '人数',
          type: 'bar',
          barWidth: '50%',
          data: [totalNum, unTrainedNum, trainedNum, correctNum, rankedNum],
          label: {
            show: true,
            position: 'top',
            formatter: '{c}'
          },
          itemStyle: {
            color: '#409eff'
          }
        }
      ]
    }
  })

  // 初始化图表
  const initChart = () => {
    if (!chartContainer.value) return
    // 如果已有实例则 dispose，防止重复初始化
    if (chartInstance) {
      chartInstance.dispose()
    }
    chartInstance = echarts.init(chartContainer.value)
    chartInstance.setOption(chartOption.value)
  }

  // 切换视图时，若当前为图表则初始化图表并调用 resize
  watch(chartType, newVal => {
    if (newVal === 'chart') {
      nextTick(() => {
        initChart()
        // 延迟调用 resize 确保容器尺寸正确
        setTimeout(() => {
          chartInstance && chartInstance.resize()
        }, 100)
      })
    }
  })

  // 当 reportData 变化且当前视图为图表时更新图表
  watch(
    () => props.reportData,
    () => {
      if (chartType.value === 'chart') {
        nextTick(() => {
          initChart()
          chartInstance && chartInstance.resize()
        })
      }
    },
    { deep: true }
  )

  onMounted(() => {
    if (chartType.value === 'chart') {
      nextTick(() => {
        initChart()
        chartInstance && chartInstance.resize()
      })
    }
  })
</script>

<template>
  <cu-dialog
    v-model="dialogVisible"
    title="训练详情"
    width="800"
    @confirm="dialogVisible = false"
    @cancel="dialogVisible = false"
  >
    <div class="mb-4">
      <el-radio-group v-model="chartType">
        <el-radio-button value="chart">图表视图</el-radio-button>
        <el-radio-button value="table">表格视图</el-radio-button>
      </el-radio-group>
    </div>
    <div class="flex mb-5 font-bold gap-8">
      <div> 应训人数：{{ props.reportData.totalNum }} 人</div>
      <div> 未训练：{{ props.reportData.unTrainedNum }} 人 </div>
      <div> 已训练：{{ props.reportData.trainedNum }} 人</div>
      <div> 已评分：{{ props.reportData.correctNum }} 人</div>
      <div> 缺训：{{ props.reportData.rankedNum }} 人</div>
    </div>

    <!-- 根据视图类型显示表格或图表 -->
    <div v-if="chartType === 'table'">
      <vxe-table
        border
        stripe
        size="medium"
        height="380"
        max-height="380"
        :data="props.reportData.trainModels"
        :row-config="{ isCurrent: true, isHover: true }"
      >
        <vxe-column type="seq" title="序号" width="90" fixed="left" align="center" />
        <vxe-column
          field="trainUser"
          title="学员学号"
          width="200"
          fixed="left"
          align="center"
          show-header-overflow
          show-overflow="title"
          show-footer-overflow
        />
        <vxe-column
          field="trainUserNick"
          title="学员姓名"
          width="160"
          fixed="left"
          align="center"
          show-header-overflow
          show-overflow="title"
          show-footer-overflow
        />
        <vxe-column field="hasFinished" title="训练状态" width="120" align="center">
          <template #default="{ row }">
            {{ getStatusLabel(row.hasFinished) }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <div v-else ref="chartContainer" style="width: 100%; height: 380px; margin: 0 auto"></div>
  </cu-dialog>
</template>

<style scoped></style>
