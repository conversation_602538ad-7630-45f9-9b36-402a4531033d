<script setup>
  import { plotToNum } from '@/utils/utils'
  import UnitCom from '@/components/UnitCom/index.vue'

  const ArbitraryWaveForm = ref({
    waveFilePath: '', //波形文件
    sampleRate: 200e3, //采样率
    triggerMode: 0, //触发模式
    notchFilterEnable: 0, //陷波滤波
    notchFilterNum: 1 //陷波数
  })

  const updateValue = (key, newValue, unit) => {
    ArbitraryWaveForm.value[key] = plotToNum(newValue + unit) // 更新表单中的原始值
  }

  const setFormData = data => {
    ArbitraryWaveForm.value = { ...data }
  }

  defineExpose({
    ArbitraryWaveForm,
    setFormData
  })
</script>

<template>
  <el-form :model="ArbitraryWaveForm" label-width="auto" label-position="left">
    <el-form-item label="波形文件：">
      <el-input
        style="width: 200px"
        placeholder="请输入波形文件路径"
        v-model="ArbitraryWaveForm.waveFilePath"
      ></el-input>
    </el-form-item>
    <el-form-item label="采样率：">
      <UnitCom
        :value="ArbitraryWaveForm.sampleRate"
        @update:value="(newValue, unit) => updateValue('sampleRate', newValue, unit)"
      />
    </el-form-item>

    <el-form-item label="触发模式：">
      <el-radio-group v-model="ArbitraryWaveForm.triggerMode" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="off" :value="0">单次触发</el-radio-button>
        <el-radio-button class="cusBtn" label="on" :value="1">重复触发</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="陷波滤波：">
      <el-radio-group
        v-model="ArbitraryWaveForm.notchFilterEnable"
        size="large"
        style="width: 200px"
      >
        <el-radio-button class="cusBtn" label="off" :value="0">停用</el-radio-button>
        <el-radio-button class="cusBtn" label="on" :value="1">启用</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="ArbitraryWaveForm.notchFilterEnable" label="陷波数：">
      <el-input-number
        style="width: 200px"
        v-model="ArbitraryWaveForm.notchFilterNum"
        :min="1"
        :max="25"
        :step="1"
      ></el-input-number>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
