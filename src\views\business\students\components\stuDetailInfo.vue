<script setup>
  import { getStudents } from '@/api/business/students'
  import { listClazz } from '@/api/business/clazz'
  import useDictStore from '@/store/modules/dict'
  import pattern from '@/utils/pattern'

  // 定义 emits
  const emit = defineEmits(['submit'])
  const props = defineProps({
    id: {
      type: String,
      default: null
    }
  })

  const dictStore = useDictStore()
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const stuDetailFormRef = ref(null)
  const sysUserSexOptions = ref([]) // 性别字典数据
  const stuMajorOptions = ref([]) // 专业字典数据
  const stuYearOptions = ref([]) // 学制字典数据
  const teachingPoliticalOptions = ref([]) // 政治面貌字典数据
  const teachingEducationOptions = ref([]) // 学历字典数据
  const clazzOptions = ref([]) // 期班字典数据
  const form = ref({})
  const loading = ref(false)

  // 表单校验规则
  const stuDetailFormRules = ref({
    stuCode: [{ required: true, message: '请输入学号', trigger: 'blur' }],
    stuName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    stuSex: [{ required: true, message: '请选择性别', trigger: 'change' }],
    stuPhone: [
      { message: '请输入手机号码', trigger: 'blur' },
      { pattern: pattern.TEL, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    stuNative: [
      { message: '请输入籍贯', trigger: 'blur' },
      { pattern: pattern.STRING, message: '请输入正确的籍贯', trigger: 'blur' }
    ],
    stuEmail: [
      { message: '请输入邮箱', trigger: 'blur' },
      { pattern: pattern.EMAIL, message: '请输入正确的邮箱', trigger: 'blur' }
    ]
  })

  const getInfo = async newId => {
    if (!newId) return
    loading.value = true
    try {
      const res = await getStudents(newId)
      form.value = res.data
    } catch (error) {
      console.error('获取学生信息失败', error)
    } finally {
      loading.value = false
    }
  }

  const handleClose = () => {
    dialogTableVisible.value = false
  }

  const confirm = () => {
    stuDetailFormRef.value.validate(valid => {
      if (!valid) return
      emit('submit', form.value)
    })
  }

  const getDictionaryData = async () => {
    const processDictionary = (dictType, targetOptions) => {
      dictStore.dict
        .filter(item => item.dictType === dictType)
        .forEach(item => {
          targetOptions.value.push({ label: item.dictLabel, value: item.dictValue })
        })
    }
    processDictionary('sys_user_sex', sysUserSexOptions)
    processDictionary('stu_major', stuMajorOptions)
    processDictionary('stu_year', stuYearOptions)
    processDictionary('teaching_political', teachingPoliticalOptions)
    processDictionary('teaching_education', teachingEducationOptions)
  }

  const getClazzData = async () => {
    await listClazz({
      pageNum: 1,
      pageSize: 100
    }).then(res => {
      clazzOptions.value = res?.data?.list.map(item => ({
        label: item.claName,
        value: item.id
      }))
    })
  }

  onMounted(async () => {
    await getDictionaryData()
    await getClazzData()
    await getInfo(props.id)
  })
</script>

<template>
  <el-dialog
    class="px-10"
    v-model="dialogTableVisible"
    :title="id ? '编辑学员' : '新增学员'"
    align="center"
    width="960px"
    @closed="handleClose"
  >
    <el-form
      v-loading="loading"
      ref="stuDetailFormRef"
      :model="form"
      :rules="stuDetailFormRules"
      label-width="auto"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学生学号：" prop="stuCode">
            <el-input v-model="form.stuCode" placeholder="请输入学生学号"></el-input>
          </el-form-item>
          <el-form-item label="学生性别：" prop="stuSex">
            <el-select v-model="form.stuSex" placeholder="请选择学生性别（必填）">
              <el-option
                v-for="item in sysUserSexOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="手机号码：" prop="stuPhone">
            <el-input v-model="form.stuPhone" placeholder="请输入手机号码："></el-input>
          </el-form-item>
          <el-form-item label="出生日期：" prop="stuBirth">
            <vxe-input
              style="width: 100%"
              v-model="form.stuBirth"
              type="date"
              placeholder="请选择出生日期"
              class="time-button"
              clearable
            />
          </el-form-item>
          <el-form-item label="专业：" prop="stuMajor">
            <el-select v-model="form.stuMajor" placeholder="请选择专业">
              <el-option
                v-for="item in stuMajorOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="入学时间：" prop="stuIndate">
            <vxe-input
              style="width: 100%"
              v-model="form.stuIndate"
              type="date"
              placeholder="请选择入学时间"
              class="time-button"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="姓名：" prop="stuName">
            <el-input v-model="form.stuName" placeholder="请输入姓名"></el-input>
          </el-form-item>
          <el-form-item label="学制：" prop="stuYear">
            <el-select v-model="form.stuYear" placeholder="请选择学制">
              <el-option
                v-for="item in stuYearOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="籍贯：" prop="stuNative">
            <el-input v-model="form.stuNative" placeholder="请输入籍贯："></el-input>
          </el-form-item>
          <el-form-item label="用户邮箱：" prop="stuEmail">
            <el-input v-model="form.stuEmail" placeholder="请输入用户邮箱："></el-input>
          </el-form-item>
          <el-form-item label="政治面貌：" prop="stuPolitical">
            <el-select v-model="form.stuPolitical" placeholder="请选择政治面貌">
              <el-option
                v-for="item in teachingPoliticalOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="期班：" prop="calId">
            <el-select v-model="form.calId" placeholder="请选择期班">
              <el-option
                v-for="item in clazzOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="text-center">
        <vxe-button @click="handleClose">取消</vxe-button>
        <vxe-button status="primary" @click="confirm">确认</vxe-button>
      </div>
    </template>
  </el-dialog>
</template>

<style></style>
