<script setup>
  import SignalModulationCom from './components/SignalModulationCom.vue'
  import FreqCom from './components/FreqCom.vue'
  import NoiseCom from './components/NoiseCom.vue'
  import { ElMessage } from 'element-plus'
  import {
    saveSignalSimulationTask,
    getSimulationTaskDetail
  } from '@/api/simulation/signalSimulation.js'
  import { useRoute, useRouter } from 'vue-router'

  let timer = null
  const route = useRoute()
  const router = useRouter()
  const activeName = ref('SignalModulationCom')
  const taskId = ref('')
  const SignalModulationComRef = ref(null)
  const FreqComRef = ref(null)
  const NoiseComRef = ref(null)
  const dialogTableVisible = ref(false)
  const loading = ref(false)
  const templateFormRef = ref(null)
  const templateForm = ref({
    taskName: '',
    taskDescribe: ''
  })
  const signalModulationComForm = ref({})
  const templateFormRules = ref({
    taskName: [
      { required: true, message: '请输入任务名称', trigger: 'blur' },
      { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
    ],
    taskDescribe: [
      { required: true, message: '请输入任务描述', trigger: 'blur' },
      { min: 3, max: 300, message: '长度在 3 到 300 个字符', trigger: 'blur' }
    ]
  })
  const deviceTestInfo = ref([
    {
      deviceAddress: '设备地址：*************',
      devicePort: '设备端口：7777',
      workStatus: '工作状态：未连接',
      faultDescription: ''
    }
  ])

  // 计算属性：判断是否有记录包含 faultDescription（非空字符串）
  const hasFaultDescription = computed(() => {
    return deviceTestInfo.value.some(
      item => item.faultDescription && item.faultDescription.trim() !== ''
    )
  })

  // 根据是否存在 faultDescription 决定表格的背景色
  const tableBgClass = computed(() => {
    return hasFaultDescription.value ? 'row-fault' : 'row-noFault'
  })

  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  /**从模板中加载 */
  const loadFromTemp = () => {
    console.log('loadFromTemp')
  }

  /** 保存模板 */
  const saveTemplate = () => {
    const realTimeBaseBandForm = SignalModulationComRef.value.SignalSettingsRef.realTimeBaseBandForm
    const dualToneForm = SignalModulationComRef.value.SignalSettingsRef.dualToneForm
    const multiToneForm = SignalModulationComRef.value.SignalSettingsRef.multiToneForm
    const amplitudeModulationForm =
      SignalModulationComRef.value.AnalogModulationRef.amplitudeModulationForm
    const freqModulationForm = SignalModulationComRef.value.AnalogModulationRef.freqModulationForm
    const pulseModulationForm = SignalModulationComRef.value.AnalogModulationRef.pulseModulationForm
    const FreqComForm = FreqComRef.value.form
    const NoiseComForm = NoiseComRef.value.form
  }

  /** 保存任务 */
  const saveTask = () => {
    if (taskId.value) {
      submitFun()
    } else {
      templateForm.value = {
        taskName: '',
        taskDescribe: ''
      }
      dialogTableVisible.value = true
    }
  }

  const preview = () => {
    console.log('preview')
  }

  const submitFun = async () => {
    const realTimeBaseBandForm = SignalModulationComRef.value.SignalSettingsRef.realTimeBaseBandForm
    const dualToneForm = SignalModulationComRef.value.SignalSettingsRef.dualToneForm
    const multiToneForm = SignalModulationComRef.value.SignalSettingsRef.multiToneForm
    const amplitudeModulationForm =
      SignalModulationComRef.value.AnalogModulationRef.amplitudeModulationForm
    const freqModulationForm = SignalModulationComRef.value.AnalogModulationRef.freqModulationForm
    const phaseModulationForm = SignalModulationComRef.value.AnalogModulationRef.phaseModulationForm
    const FreqComForm = FreqComRef.value.form
    const NoiseComForm = NoiseComRef.value.form
    const formData = {
      ...templateForm.value,
      id: taskId.value || null,
      signalSimulation: {
        freq: FreqComForm,
        noise: NoiseComForm,
        sigDemod: {
          analogModulation: {
            amplitudeModulation: amplitudeModulationForm,
            freqModulation: freqModulationForm,
            phaseModulation: phaseModulationForm
          },
          setting: {
            realTimeBaseBand: realTimeBaseBandForm,
            dualTone: dualToneForm,
            multiTone: multiToneForm
          }
        }
      }
    }
    await saveSignalSimulationTask(formData).then(res => {
      if (res.code === 200) {
        ElMessage.success('保存成功')
        router.push({ path: '/signalSimulationTask' })
      } else {
        ElMessage.error(res.msg)
      }
    })
  }

  /**
   * 保存信号模拟任务
   * @description: 处理确认按钮点击事件
   * @return {Promise<void>}
   * @description: 保存模板表单并关闭对话框，并将表单数据发送到后端
   */
  const handleConfirm = async () => {
    templateFormRef.value.validate(async valid => {
      if (valid) {
        submitFun()
        dialogTableVisible.value = false
      } else {
        return false
      }
    })
  }

  const handleCancel = () => {
    dialogTableVisible.value = false
  }

  /**
   * 根据Id获取信号模拟任务数据
   */
  const getTaskById = async () => {
    loading.value = true
    await getSimulationTaskDetail({ id: taskId.value })
      .then(res => {
        if (res.code === 200) {
          setFormData(res.data)
          templateForm.value.taskName = res.data.taskName
          templateForm.value.taskDescribe = res.data.taskDescribe
          signalModulationComForm.value = res.data.signalSimulation.sigDemod
        } else {
          ElMessage.error(res.msg)
        }
      })
      .finally(() => {
        timer = setTimeout(() => {
          loading.value = false
        }, 200)
      })
  }

  const setFormData = data => {
    nextTick(() => {
      SignalModulationComRef.value.SignalSettingsRef.setFormData(
        data.signalSimulation.sigDemod.setting
      )
      SignalModulationComRef.value.AnalogModulationRef.setFormData(
        data.signalSimulation.sigDemod.analogModulation
      )
      FreqComRef.value.setFormData(data.signalSimulation.freq)
      NoiseComRef.value.setFormData(data.signalSimulation.noise)
    })
  }

  onMounted(() => {
    if (route.query?.id) {
      taskId.value = route.query.id
      getTaskById()
    }
  })

  onBeforeUnmount(() => {
    clearTimeout(timer)
  })

  // 监听路由 query 变化（如果组件复用时切换路由，确保能响应变化）
  watch(
    () => route.query.id,
    (newId, oldId) => {
      if (newId && newId !== oldId) {
        taskId.value = newId
        getTaskById()
      }
    }
  )
</script>

<template>
  <div class="relative w-full h-full">
    <cu-title title="信号模拟"></cu-title>
    <div class="flex gap-8" v-loading="loading">
      <el-tabs
        class="w-5/6"
        v-model="activeName"
        type="border-card"
        @tab-click="handleClick"
        tab-position="left"
      >
        <el-tab-pane label="信号与解调" name="SignalModulationCom">
          <SignalModulationCom ref="SignalModulationComRef" :form-data="signalModulationComForm" />
        </el-tab-pane>
        <el-tab-pane label="频率" name="FreqCom"><FreqCom ref="FreqComRef" /></el-tab-pane>
        <el-tab-pane label="噪声" name="NoiseCom"><NoiseCom ref="NoiseComRef" /></el-tab-pane>
      </el-tabs>
      <div class="funBtn flex flex-col justify-center gap-10">
        <cu-button content="从参数模板加载" @click="loadFromTemp"></cu-button>
        <cu-button content="保存为参数模板" @click="saveTemplate"></cu-button>
        <cu-button content="保存任务" @click="saveTask"></cu-button>
        <cu-button content="预览" @click="preview"></cu-button>
      </div>
    </div>

    <!-- 设备信息 -->
    <div class="absolute -bottom-4 -right-4">
      <vxe-table
        class="max-w-[1000px]"
        :class="tableBgClass"
        :show-header="false"
        :data="deviceTestInfo"
        border
      >
        <vxe-column field="deviceAddress" title="设备地址" width="160" align="center" />
        <vxe-column field="devicePort" title="设备端口" width="100" align="center" />
        <vxe-column field="workStatus" title="工作状态" width="100" align="center" />
        <!-- 如果存在 faultDescription，则显示该列 -->
        <vxe-column
          v-if="hasFaultDescription"
          field="faultDescription"
          title="故障描述"
          align="center"
        />
      </vxe-table>
    </div>
    <!-- 信号模拟任务保存 -->
    <cu-dialog
      v-model="dialogTableVisible"
      title="保存信号模拟任务"
      width="600px"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    >
      <el-form ref="templateFormRef" :model="templateForm" :rules="templateFormRules">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="templateForm.taskName" min="3" max="50" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务描述" prop="taskDescribe">
          <el-input
            v-model="templateForm.taskDescribe"
            type="textarea"
            min="3"
            max="300"
            :rows="10"
            placeholder="请输入不超过300字的任务描述"
          />
        </el-form-item>
      </el-form>
    </cu-dialog>
  </div>
</template>

<style scoped lang="scss">
  :deep(.vxe-table--body) {
    color: #fff !important;
  }
  :deep(.row-noFault .vxe-table--body) {
    background-color: #56bcbe !important ;
  }
  :deep(.row-fault .vxe-table--body) {
    background-color: #e89e42 !important ;
  }
  :deep(.funBtn .vxe-button + .vxe-button) {
    margin-left: 0 !important;
  }
</style>
