<template>
  <div class="eye-container">
    <chart-header title="眼图" name="Eye" />
    <el-row :gutter="20">
      <el-col :span="24">
        <div id="iEye" />
      </el-col>
      <!-- <el-col :span="12">
        <div id="qEye" />
      </el-col> -->
    </el-row>
  </div>
</template>

<script setup>
  // 眼图
  import useChartsStore from '@/store/modules/charts'
  import ChartHeader from '@/views/analyse/header/ChartHeader'
  import useDmFormStore from '@/store/modules/form/dmForm'
  import useChartOptions from '@/common/hooks/chartOptions'
  import Highcharts from '@/plugins/highcharts'
  import Interval from '@/common/classes/interval'
  import ci from '@/common/chartInstances'
  import { cloneDeep, round } from 'lodash'

  defineComponent([ChartHeader])

  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })

  const iContainer = ref(null)
  let qContainer = null
  let interval = null
  const chartsStore = useChartsStore()
  const iData = ref([])
  // const qData = ref([])
  // const { settings } = chartsStore
  // 一组几个点
  // const groupNum = ref(0)
  const form = useDmFormStore()
  const { themeStyle, chartOptions } = useChartOptions()
  const isStatic = chartsStore.static.enabled
  // const generateEyeData = (data, eyestep = 25) => {
  //   const result = []
  //   for (let i = 0; i < data.length; i += eyestep) {
  //     let n = 0,
  //       temp = []
  //     while (n < eyestep) {
  //       temp.push([n, data[i + n]])
  //       n++
  //     }
  //     result.push(temp)
  //   }
  //   return result
  // }
  const generateEyeData = (data, eyestep = 25) => {
    if (eyestep <= 0) {
      // throw new Error('eyestep must be a positive integer')
      return []
    }
    const result = []
    const totalSteps = Math.ceil(data.length / eyestep)
    for (let i = 0; i < totalSteps; i++) {
      const temp = []
      const start = i * eyestep
      const end = Math.min(start + eyestep, data.length)
      for (let n = 0; n < end - start; n++) {
        temp.push([n, data[start + n]])
      }
      result.push(temp)
    }
    return result
  }

  const genearteChartsOptions = data => {
    const currentOptions = cloneDeep(chartOptions.value)
    currentOptions.yAxis.min = -1.6
    currentOptions.yAxis.max = 1.6
    currentOptions.xAxis.min = 0
    const max = Math.ceil(props.data.eyestep) - 1
    currentOptions.xAxis.max = max
    currentOptions.xAxis.tickInterval = max / 10
    currentOptions.xAxis.labels.formatter = function () {
      if (this.value === 0) {
        return -1
      }
      if (this.value === max / 2) {
        return 0
      }
      if (this.value === max) {
        return 1
      }
    }
    currentOptions.xAxis.labels.renderFormatter = function () {
      return round(-1 + (this.value / max) * 2, 2)
    }
    currentOptions.yAxis.tickAmount = 11
    currentOptions.yAxis.tickInterval = 0.32
    currentOptions.yAxis.labels.formatter = function () {
      return round(this.value, 1)
    }
    const t = isStatic ? data.slice(0, chartsStore.static.dmLimit) : data.slice(0, form.resultLen)
    currentOptions.series = t.map(item => ({
      type: 'spline',
      marker: {
        enabled: false
      },
      enableMouseTracking: false,
      animation: false,
      lineWidth: 0.5,
      color: themeStyle.value.lineColor,
      data: item
    }))
    return currentOptions
  }
  const initCharts = () => {
    nextTick(() => {
      if (iContainer.value) {
        iContainer.value.destroy() // 销毁 Highcharts 实例
      }
      iContainer.value = new Highcharts.Chart('iEye', genearteChartsOptions(iData.value))
      ci.set(iContainer.value, 'iEye')
    })

    // qContainer = new Highcharts.Chart('qEye', genearteChartsOptions(qData.value))
    // ci.set(qContainer, 'qEye')
  }
  const init = async () => {
    // const eyestep = Math.ceil(props.data.eyestep)
    // const quarterLength = Math.floor(props.data.pdIBase?.length / 8)
    // const quarterData = props.data.pdIBase.slice(0, quarterLength)
    // iData.value = generateEyeData(quarterData, eyestep)
    // iData.value = generateEyeData(props.data.pdIBase, eyestep)
    // qData.value = generateEyeData(props.data.pdQBase, eyestep)
    // initCharts()
  }

  // watchEffect(() => {
  //   const { modulation } = form
  //   groupNum.value = Math.pow(2, modulation)
  // })

  watch(
    () => themeStyle.value,
    () => {
      iContainer.value.update(genearteChartsOptions(iData.value), true)
      // qContainer.update(genearteChartsOptions(qData.value), true)
    }
  )

  watch(
    () => props.data,
    newVal => {
      if (newVal) {
        const eyestep = Math.ceil(newVal.eyestep)
        const quarterLength = Math.min(Math.floor(newVal.pdIBase?.length), eyestep * 60)
        const quarterData = newVal.pdIBase.slice(0, quarterLength)
        iData.value = generateEyeData(quarterData, eyestep)
        // qData.value = generateEyeData(props.data.pdQBase, eyestep)
        // if (!iContainer.value) {
        initCharts()
        // }
        // 使用 setData 更新数据，而不是 update 整个图表
        // else if (iContainer.value || qContainer) {
        //   requestAnimationFrame(() => {
        //     iData.value.forEach((data, index) => {
        //       if (iContainer.value.series[index]) {
        //         iContainer.value.series[index].setData(data, false, false, false)
        //       }
        //     })
        //     // qData.value.forEach((data, index) => {
        //     //   if (qContainer.series[index]) {
        //     //     qContainer.series[index].setData(data, false, false, false)
        //     //   }
        //     // })
        //     iContainer.value.redraw()
        //     // qContainer.redraw()
        //   })
        // }
      }
    },
    {
      deep: true
    }
  )

  onMounted(() => {
    init()
  })

  onUnmounted(() => {
    if (interval) {
      interval.destroy()
    }
    if (iContainer.value) {
      iContainer.value.destroy() // 销毁 Highcharts 实例
    }
    // if (qContainer) {
    //   qContainer.destroy() // 销毁 Highcharts 实例
    // }
  })
</script>

<style scoped>
  .eye-container {
    background-color: var(--background-color);
  }
</style>
