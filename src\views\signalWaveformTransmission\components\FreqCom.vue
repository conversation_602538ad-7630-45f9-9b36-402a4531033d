<script setup>
  import { plotToNum } from '@/utils/utils'
  import UnitCom from '@/components/UnitCom/index.vue'

  const form = ref({
    freq: 200e3, //频率（连续波）
    freqStep: 200e3, // 频率步进
    freqOffset: 200e3, //频率偏置
    freqReferenceSwitch: 0, // 频率参考
    freqReference: 200e3, //频率参考值
    freqMul: 3 //频率倍乘
  })

  const updateValue = (key, newValue, unit) => {
    form.value[key] = plotToNum(newValue + unit) // 更新表单中的原始值
  }

  const setFormData = data => {
    form.value = { ...data }
  }

  defineExpose({
    form,
    setFormData
  })
</script>

<template>
  <el-form :model="form" label-width="auto" label-position="left">
    <el-form-item label="频率（连续波）：">
      <!-- 显示转换后的值 -->
      <UnitCom
        :value="form.freq"
        @update:value="(newValue, unit) => updateValue('freq', newValue, unit)"
      />
    </el-form-item>
    <el-form-item label="频率步进：">
      <UnitCom
        :value="form.freqStep"
        @update:value="(newValue, unit) => updateValue('freqStep', newValue, unit)"
      />
    </el-form-item>
    <el-form-item label="频率偏置：">
      <UnitCom
        :value="form.freqOffset"
        @update:value="(newValue, unit) => updateValue('freqOffset', newValue, unit)"
      />
    </el-form-item>
    <el-form-item label="频率参考：">
      <el-radio-group v-model="form.freqReferenceSwitch" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="off" :value="0">关</el-radio-button>
        <el-radio-button class="cusBtn" label="on" :value="1">开</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="频率参考值：">
      <UnitCom
        :value="form.freqReference"
        @update:value="(newValue, unit) => updateValue('freqReference', newValue, unit)"
      />
    </el-form-item>
    <el-form-item label="频率倍乘：">
      <el-input style="width: 200px" v-model="form.freqMul" />
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
