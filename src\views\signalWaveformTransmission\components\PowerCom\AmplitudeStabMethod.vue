<script setup>
  const form = ref({
    type: 0,
    externalCoupling: 0
  })

  const reset = () => {
    form.value = {
      type: 0,
      externalCoupling: 0
    }
  }

  const setFormData = data => {
    form.value = { ...data }
  }

  defineExpose({
    form,
    reset,
    setFormData
  })
</script>

<template>
  <el-form :model="form" label-width="auto" label-position="left">
    <el-form-item label="稳幅方式：">
      <el-radio-group v-model="form.type" size="large" style="width: 300px">
        <el-radio-button class="cusBtn" label="inner" :value="0">内部</el-radio-button>
        <el-radio-button class="cusBtn" label="outside" :value="1">外部</el-radio-button>
        <el-radio-button class="cusBtn" label="sourceModule" :value="2"> 源模块 </el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="外检波耦合系数：">
      <el-input style="width: 200px" v-model="form.externalCoupling">
        <template #append> dB </template>
      </el-input>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 33.3%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
