<script setup>
  import useDictStore from '@/store/modules/dict'
  import { useRouter } from 'vue-router'
  import CuIQFilesDialog from '@/components/CuIQFilesDialog/index.vue'
  import { getTM, saveTaskAll, addOrUpdateTM } from '@/api/simulation/trainingMission.js'
  import { ElMessage } from 'element-plus'
  import useChartsStore from '@/store/modules/charts'
  import { selectOne } from '@/api/file/index.js'

  const props = defineProps({
    id: {
      type: String,
      default: null
    }
  })

  const router = useRouter()
  const dictStore = useDictStore()
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const emit = defineEmits(['reloadList'])
  const IQFileDialog = ref(false)
  const loading = ref(false)
  const chartsStore = useChartsStore()

  const trainTaskModel = ref({
    taskName: '', // 任务名称
    createUser: '', // 发起人
    createTime: '', // 创建时间
    taskFile: '', // 识别文件
    taskFileId: '' // 识别文件ID
  })

  const signalRecognitionAnswer = ref({
    // 输入值（存储为基本单位：frequency 为 Hz, sigWidth 为 Hz）
    sigFeq: 200e6, // 频率（单位：Hz）
    sigWidth: 100e3, // 带宽（单位：Hz）
    sigAmp: -50, // 功率
    sigType: '0', // 信号类型
    modType: '0', // 解调类型
    isHop: 0, // 跳频信号
    isSpr: 0, // 扩频信号
    // 动态分值和扣分规则
    sigFeqPoint: 10,
    sigFeqStepFeq: 1,
    sigFeqStepPoint: 0.2,

    sigWidthPoint: 10,
    sigWidthStepFeq: 1,
    sigWidthStepPoint: 0.2,

    sigAmpPoint: 10,
    sigAmpStepDbm: 1,
    sigAmpStepPoint: 0.2,

    sigTypePoint: 20, // 信号类型扣分规则
    modTypePoint: 20, // 解调类型扣分规则
    isHopPoint: 15, // 跳频信号扣分规则
    isSprPoint: 15 // 扩频信号扣分规则
  })

  const procedureTableRef = ref(null) // 表格引用
  const signalRecognitionSteps = ref([]) // 操作步骤列表
  const ids = ref([]) // 选中步骤ID数组
  const signalTypeOptions = reactive([])
  const operationStepsOptions = reactive([])
  const modulationTypeOptions = reactive([])
  const footerMethod = () => {
    return [['合计', '', stepScore.value]]
  }
  // computed 属性：显示 MHz 的频率
  const sigFeq = computed({
    get: () => signalRecognitionAnswer.value.sigFeq / 1e6,
    set: val => {
      signalRecognitionAnswer.value.sigFeq = val * 1e6
    }
  })

  // computed 属性：显示 kHz 的带宽
  const sigWidth = computed({
    get: () => signalRecognitionAnswer.value.sigWidth / 1e3,
    set: val => {
      signalRecognitionAnswer.value.sigWidth = val * 1e3
    }
  })

  const resultScore = computed(() => {
    return (
      Number(signalRecognitionAnswer.value.sigFeqPoint) +
      Number(signalRecognitionAnswer.value.sigWidthPoint) +
      Number(signalRecognitionAnswer.value.sigAmpPoint) +
      Number(signalRecognitionAnswer.value.sigTypePoint) +
      Number(signalRecognitionAnswer.value.modTypePoint) +
      Number(signalRecognitionAnswer.value.isHopPoint) +
      Number(signalRecognitionAnswer.value.isSprPoint)
    )
  })

  const stepScore = computed(() => {
    return signalRecognitionSteps.value.reduce((total, item) => total + Number(item.optPoint), 0)
  })

  // 缓存文件数据转化
  const transToStore = row => {
    const result = {}
    const requiredKeys = [
      'id',
      'samplingRate',
      'bitRate',
      'centerFreqIn',
      'dataType',
      'debugMode',
      'fftSize',
      'fileType',
      'intermediateFrequencyBandwidth',
      'uploadTime',
      'iqReverse',
      'logarithm',
      'startOffset',
      'startOffLength',
      'cutoffLength',
      'steplen',
      'powerMultiple'
    ]
    Object.keys(row).forEach(key => {
      if (!requiredKeys.includes(key)) {
        return
      }
      if (typeof row[key] === 'string' && row[key].includes('Hz')) {
        result[key] = displayUnitConversion(row[key])
      } else {
        result[key] = row[key]
      }
    })
    result.fileName = row.fileName
    return result
  }

  /**
   * 获取字典数据
   */
  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'operation_steps')
      .forEach(item => {
        operationStepsOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
    dictStore.dict
      .filter(item => item.dictType === 'signal_type')
      .forEach(item => {
        signalTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
    dictStore.dict
      .filter(item => item.dictType === 'modulation_type')
      .forEach(item => {
        modulationTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  // 辅助方法：根据操作步骤的值获取对应标签
  const getStepLabel = optStepStr => {
    const found = operationStepsOptions.find(item => item.value === optStepStr.toString())
    return found ? found.label : optStepStr.toString()
  }

  // 跳到分析页面
  const goAnalysisView = async () => {
    await selectOne({ id: trainTaskModel.value.taskFileId }).then(res => {
      chartsStore.setFileInfo(transToStore(res.data), '0')
      chartsStore.writeStorage()
    })
    router.push({
      name: 'SignalAnalyse'
    })
  }

  const changeClick = () => {
    IQFileDialog.value = true
  }

  /**
   * IQ数据修改
   * @param row 当前行数据
   */
  const IQFileEdit = async row => {
    trainTaskModel.value.taskFile = row.fileName
    trainTaskModel.value.taskFileId = row.id
    await addOrUpdateTM(trainTaskModel.value).then(async res => {
      if (res.code === 200) {
        await initData()
        ElMessage.success(res.msg)
      } else {
        ElMessage.error(res.msg)
      }
    })
  }

  /**
   * IQ数据选择事件
   */
  const selectChangeEvent = () => {
    const selectRecords = procedureTableRef.value.getCheckboxRecords()
    ids.value = []
    selectRecords.forEach(item => {
      ids.value.push(item.id)
    })
    if (selectRecords.length !== ids.value.length) {
      ids.value = []
      selectRecords.forEach(item => {
        ids.value.push(item.id)
      })
    }
  }

  /**
   * 添加操作步骤
   */
  const addProcedure = () => {
    signalRecognitionSteps.value.push({
      id: signalRecognitionSteps.value.length + 1,
      optPoint: 10,
      optStep: 0,
      optStepStr: '0', // 默认选中 operationStepsOptions 中的第一个选项
      stepNum: signalRecognitionSteps.value.length + 1 // 根据 optStepStr 获取对应的标签
    })
  }

  /**
   * 删除操作步骤
   */
  const deleteProcedure = async () => {
    if (ids.value.length !== 0) {
      signalRecognitionSteps.value = signalRecognitionSteps.value.filter(
        item => !ids.value.includes(item.id)
      )
    } else {
      ElMessage.error('请选择要删除的操作步骤')
    }
  }

  /**
   * 保存并关闭
   */
  const confirm = () => {
    const params = {
      trainTaskModel: trainTaskModel.value,
      signalRecognitionAnswer: signalRecognitionAnswer.value,
      signalRecognitionSteps: signalRecognitionSteps.value.map(item => {
        return {
          ...item,
          optStep: Number(item.optStepStr) + 1
        }
      })
    }
    saveTaskAll(params).then(res => {
      if (res.code === 200) {
        ElMessage.success(res.msg)
        dialogTableVisible.value = false
        emit('reloadList')
      } else {
        ElMessage.error(res.msg)
      }
    })
  }

  const initData = async () => {
    loading.value = true
    await getTM({ taskId: props.id })
      .then(res => {
        if (res.code === 200) {
          if (res.data?.trainTaskModel) {
            trainTaskModel.value = res.data?.trainTaskModel
          }
          if (res.data?.signalRecognitionAnswer) {
            signalRecognitionAnswer.value = res.data.signalRecognitionAnswer
          }
          if (res.data.signalRecognitionSteps.length > 0) {
            signalRecognitionSteps.value = res.data.signalRecognitionSteps
          }
        } else {
          ElMessage.error(res.msg)
        }
      })
      .finally(() => {
        loading.value = false
      })
  }

  onMounted(async () => {
    await getDictionaryData()
    await initData()
  })
</script>

<template>
  <cu-dialog
    class="px-10"
    v-model="dialogTableVisible"
    title="识别训练答案"
    align="center"
    width="860"
    @confirm="confirm"
    @cancel="dialogTableVisible = false"
  >
    <div v-loading="loading">
      <!-- 任务概况 -->
      <CuTitle class="font-bold" :title="trainTaskModel.taskName"></CuTitle>
      <div class="mb-6">
        <div class="flex justify-start mb-2">
          <div class="flex items-center justify-center mr-40">
            <el-icon :size="24"><Avatar /></el-icon>
            <div class="font-bold mx-2">{{ trainTaskModel.createUser }}</div>
          </div>
          <div class="flex items-center justify-center">
            <el-icon :size="24"><Timer /></el-icon>
            <div class="font-bold mx-2">{{ trainTaskModel.createTime }}</div>
          </div>
        </div>
        <div class="flex items-center">
          <el-icon :size="24"><Document /></el-icon>
          <div class="font-bold mx-2 w-3/4 text-left">{{ trainTaskModel.taskFile }}</div>
          <cu-button content="分析" @click="goAnalysisView"></cu-button>
          <cu-button content="更换" @click="changeClick"></cu-button>
        </div>
      </div>

      <!-- 识别结果答案  -->
      <CuTitle class="font-bold" title="识别结果答案"></CuTitle>
      <el-form :model="signalRecognitionAnswer" label-width="auto" label-position="left">
        <div class="mb-3 text-center text-lg text-[#5ac0c1] font-bold"
          >结果分 {{ resultScore }}
        </div>
        <!-- 信号频率 -->
        <el-form-item label="信号频率：">
          <el-input style="width: 220px" v-model="sigFeq">
            <template #append> MHz </template>
          </el-input>
          <div class="ml-5 font-bold">分值：</div>
          <el-input
            style="width: 60px"
            v-model="signalRecognitionAnswer.sigFeqPoint"
            :min="0"
            type="number"
          >
          </el-input>
          <div class="ml-5 font-bold">每差：</div>
          <el-input
            style="width: 100px"
            v-model="signalRecognitionAnswer.sigFeqStepFeq"
            :min="0"
            type="number"
          >
          </el-input>
          <div class="w-14 font-bold">MHz 扣</div>
          <el-input
            style="width: 80px"
            v-model="signalRecognitionAnswer.sigFeqStepPoint"
            :min="0"
            :step="0.1"
            type="number"
          >
          </el-input>
          <div class="font-bold">分</div>
        </el-form-item>
        <!-- 信号带宽 -->
        <el-form-item label="信号带宽：">
          <el-input style="width: 220px" v-model="sigWidth">
            <template #append> kHz </template>
          </el-input>
          <div class="ml-5 font-bold">分值：</div>
          <el-input
            style="width: 60px"
            v-model="signalRecognitionAnswer.sigWidthPoint"
            :min="0"
            type="number"
          >
          </el-input>
          <div class="ml-5 font-bold">每差：</div>
          <el-input
            style="width: 100px"
            v-model="signalRecognitionAnswer.sigWidthStepFeq"
            :min="0"
            type="number"
          >
          </el-input>
          <div class="w-14 font-bold">kHz 扣</div>
          <el-input
            style="width: 80px"
            v-model="signalRecognitionAnswer.sigWidthStepPoint"
            :min="0"
            :step="0.1"
            type="number"
          >
          </el-input>
          <div class="font-bold">分</div>
        </el-form-item>
        <!-- 信号功率 -->
        <el-form-item label="信号功率：">
          <el-input style="width: 220px" v-model="signalRecognitionAnswer.sigAmp">
            <template #append> dBm </template>
          </el-input>
          <div class="ml-5 font-bold">分值：</div>
          <el-input
            style="width: 60px"
            v-model="signalRecognitionAnswer.sigAmpPoint"
            :min="0"
            type="number"
          >
          </el-input>
          <div class="ml-5 font-bold">每差：</div>
          <el-input
            style="width: 100px"
            v-model="signalRecognitionAnswer.sigAmpStepDbm"
            :min="0"
            type="number"
          >
          </el-input>
          <div class="w-14 font-bold">dBm 扣</div>
          <el-input
            style="width: 80px"
            v-model="signalRecognitionAnswer.sigAmpStepPoint"
            :min="0"
            :step="0.1"
            type="number"
          >
          </el-input>
          <div class="font-bold">分</div>
        </el-form-item>
        <!-- 信号类型 -->
        <el-form-item label="信号类型：">
          <vxe-select
            style="width: 220px"
            v-model="signalRecognitionAnswer.sigType"
            placeholder="请选择信号类型"
          >
            <vxe-option
              v-for="item in signalTypeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </vxe-select>
          <div class="ml-5 font-bold">分值：</div>
          <el-input
            style="width: 60px"
            v-model="signalRecognitionAnswer.sigTypePoint"
            :min="0"
            type="number"
          >
          </el-input>
        </el-form-item>
        <!-- 调制样式 -->
        <el-form-item label="调制样式：">
          <vxe-select
            style="width: 220px"
            v-model="signalRecognitionAnswer.modType"
            placeholder="请选择调制样式"
          >
            <vxe-option
              v-for="item in modulationTypeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </vxe-select>
          <div class="ml-5 font-bold">分值：</div>
          <el-input
            style="width: 60px"
            v-model="signalRecognitionAnswer.modTypePoint"
            :min="0"
            type="number"
          >
          </el-input>
        </el-form-item>
        <!-- 跳频信号 -->
        <el-form-item label="跳频信号：" prop="isHop">
          <el-radio-group v-model="signalRecognitionAnswer.isHop" size="large" style="width: 220px">
            <el-radio-button class="cusBtn" :label="1" :value="1">是</el-radio-button>
            <el-radio-button class="cusBtn" :label="0" :value="0">否</el-radio-button>
          </el-radio-group>
          <div class="ml-5 font-bold">分值：</div>
          <el-input
            style="width: 60px"
            v-model="signalRecognitionAnswer.isHopPoint"
            :min="0"
            type="number"
          >
          </el-input>
        </el-form-item>
        <!-- 扩频信号 -->
        <el-form-item label="扩频信号：" prop="isSpr">
          <el-radio-group v-model="signalRecognitionAnswer.isSpr" size="large" style="width: 220px">
            <el-radio-button class="cusBtn" :label="1" :value="1">是</el-radio-button>
            <el-radio-button class="cusBtn" :label="0" :value="0">否</el-radio-button>
          </el-radio-group>
          <div class="ml-5 font-bold">分值：</div>
          <el-input
            style="width: 60px"
            v-model="signalRecognitionAnswer.isSprPoint"
            :min="0"
            type="number"
          >
          </el-input>
        </el-form-item>
      </el-form>

      <!-- 操作步骤答案 -->
      <div class="font-bold text-left text-lg my-3">
        <CuTitle title="操作步骤答案"></CuTitle>
        <div class="text-center text-[#5ac0c1]">步骤分 {{ stepScore }} </div>
        <cu-button content="新增" @click="addProcedure"></cu-button>
        <cu-button content="删除" @click="deleteProcedure"></cu-button>
      </div>
      <vxe-table
        border
        stripe
        ref="procedureTableRef"
        size="medium"
        height="200"
        :show-footer="true"
        :data="signalRecognitionSteps"
        :keep-source="true"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          showStatus: true
        }"
        :footer-method="footerMethod"
        :checkbox-config="{ labelField: 'stepNum' }"
        :row-config="{ isCurrent: true, isHover: true }"
        @checkbox-change="selectChangeEvent"
        @checkbox-all="selectChangeEvent"
      >
        <vxe-column
          type="checkbox"
          field="id"
          title="步骤序号"
          width="120"
          fixed="left"
          align="center"
        />
        <vxe-column field="optStepStr" title="操作步骤" align="center" :edit-render="{}">
          <template #default="{ row }">
            {{ getStepLabel(row.optStepStr) }}
          </template>
          <template #edit="{ row }">
            <select v-model="row.optStepStr" class="select-style" transfer>
              <option
                v-for="(item, index) in operationStepsOptions"
                :key="index"
                :value="item.value"
                :label="item.label"
              />
            </select>
          </template>
        </vxe-column>
        <vxe-column field="optPoint" title="分值" width="80" align="center" :edit-render="{}">
          <template #edit="{ row }">
            <vxe-input v-model="row.optPoint" :min="0" type="number" />
          </template>
        </vxe-column> </vxe-table
    ></div>
  </cu-dialog>

  <CuIQFilesDialog
    v-if="IQFileDialog"
    v-model="IQFileDialog"
    @upward-fun="IQFileEdit"
  ></CuIQFilesDialog>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
  .select-style {
    background-color: var(--background-color);
    border: 1px solid var(--chart-text-color);
    border-radius: 4px;
    padding: 8px;
    color: var(--chart-text-color);
    width: 100%;
    cursor: pointer;
  }
</style>
