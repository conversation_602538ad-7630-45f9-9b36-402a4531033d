<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection = rows)"
    />
    <mtDetailInfo ref="refsMt" @actSuccess="getList()" />
    <c-import ref="refsImport" />
  </div>
</template>
<script>
  import * as mtApi from '@/api/business/students'
  import mtDetailInfo from './tmpInfo.vue'
  export default {
    name: 'Sctudents',
    components: { mtDetailInfo },
    data() {
      return {
        selection: [],
        tableFormConfig: {
          title: '学生管理',
          searchConfig: {
            labelWidth: '6em',
            formConfig: [
              { title: '学生姓名', name: 'stuName' },
              { title: '手机号码', name: 'stuPhone' },
              {
                title: '入学时间',
                type: 'rangNum',
                min: 1949,
                max: new Date().getFullYear(),
                name: 'stuIndate',
                placeholder: `开始年份,结束年份`,
                controls: false,
                childName: ['start', 'end']
              }
            ],
            loadData: p => {
              // p.start = p.start || 1949
              // p.end = p.end || new Date().getFullYear()
              return mtApi.listStudents(p)
            }
          },
          actions: [
            {
              label: '新增',
              type: 'primary',
              icon: 'el_Plus',
              permission: ['business:students:add'],
              click: () => {
                this.$refs.refsMt.show()
              }
            },
            {
              label: '导出',
              icon: 'el_upload',
              type: 'warning',
              permission: ['business:students:export'],
              click: row =>
                this.download(
                  'business/students/export',
                  {
                    // ...queryParams.value
                  },
                  `学生管理_${new Date().getTime()}.xlsx`
                )
            },
            {
              type: 'danger',
              label: '删除',
              permission: ['business:students:remove'],
              disabled: () => !this.selection.length,
              icon: 'el_Delete',
              click: row => this.deleteAll()
            },
            {
              label: '导入',
              type: 'info',
              icon: 'el_download',
              click: row => {
                this.importFile()
              }
            }
          ],
          tableConfig: {
            isChecked: true,
            cols: [
              { label: '学生学号', prop: 'stuCode' },
              { label: '学生姓名', prop: 'stuName', minWidth: 200 },
              { label: '学生性别', prop: 'stuSex', type: 'dict', dictName: 'sys_user_sex' },
              { label: '所属期班', renderFun: (h, { row }) => h('span', row.srClazz?.claName) },
              { label: '出生日期', prop: 'stuBirth' },
              { label: '手机号码', prop: 'stuPhone' },
              { label: '入学时间', prop: 'stuIndate' },
              { label: '专业', prop: 'stuMajor', type: 'dict', dictName: 'stu_major' },
              { label: '学制', prop: 'stuYear', type: 'dict', dictName: 'stu_year' },
              {
                label: '政治面貌',
                prop: 'stuPolitical',
                type: 'dict',
                dictName: 'teaching_political'
              },
              { label: '学历', prop: 'stuEducation', type: 'dict', dictName: 'teaching_education' },
              { label: '籍贯', prop: 'stuNative', minWidth: 250 },
              {
                label: '操作',
                type: 'action',
                width: '80',
                actions: [
                  {
                    title: '编辑',
                    icon: 'el_Edit',
                    permission: ['business:students:edit'],
                    click: row => this.$refs.refsMt.show(row.stuId)
                  },
                  {
                    title: '删除',
                    icon: 'el_Delete',
                    permission: ['business:students:remove'],
                    click: row => this.deleteAll(row.stuId)
                  }
                ]
              }
            ]
          }
        }
      }
    },
    methods: {
      getList(data = null) {
        this.$refs.refsTableForm.loadData(data)
      },
      //批量删除模板
      deleteAll(id = null) {
        if (!this.selection.length && !id) return this.$model.msgWarning('请选择要操作的数据')
        this.$modal.confirm('确认删除所选学生吗？', '提示').then(() => {
          let ids = id ? [id] : this.selection.map(v => v.stuId)
          mtApi.delStudents(ids).then(res => {
            this.$modal.msgSuccess(res.msg)
            this.getList()
          })
        })
      },
      importFile() {
        this.$refs.refsImport.show({
          tmpUrl: '/business/students/excelTemplate',
          tmpName: '学生管理',
          uploadUrl: '/business/students/importData',
          ok: res => {
            this.getList()
          }
        })
      }
    }
  }
</script>
