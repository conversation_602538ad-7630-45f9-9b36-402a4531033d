<script setup>
  import { plotToNum } from '@/utils/utils'
  import UnitCom from '@/components/UnitCom/index.vue'
  import useDictStore from '@/store/modules/dict'
  import { allWavePlan } from '@/api/simulation/waveformPlan.js'

  const dictStore = useDictStore()
  const activeName = ref('AM') //默认展示幅度调制
  const standardWaveformOptions = reactive([])
  const customWaveformOptions = reactive([])

  // 幅度调制
  const amplitudeModulationForm = ref({
    enable: false,
    modDeep: false, //深度调幅开关
    userDefinedWaveform: false, //自定义波形开关
    waveformType: '', //标准波形
    userDefinedWaveformType: '', //自定义波形
    amplitudeModType: 0, //调幅类型
    modRate: 200e3, //调制率
    modDeepValue: 50 //调制深度
  })

  // 频率调制
  const freqModulationForm = ref({
    enable: false,
    freqOne: 200e3, //频率一
    freqTwo: 200e3, //频率二
    freqTwoAP: 50, //频率2幅度占比
    freqModOrigin: 0, //调制源
    externalCoupMod: 0 //外部输入耦合方式
  })

  // 相位调制
  const phaseModulationForm = ref({
    enable: false,
    userDefinedWaveform: false, //自定义波形开关
    waveformType: '', //标准波形
    userDefinedWaveformType: '', //自定义波形
    phaseModBw: 0, //调相带宽
    modRate: 200e3, //调制率
    phaseModOffset: 10 //调相相偏
  })

  const reset = () => {
    amplitudeModulationForm.value = {
      enable: false,
      modDeep: false, //深度调幅开关
      userDefinedWaveform: false, //自定义波形开关
      waveformType: '', //标准波形
      userDefinedWaveformType: '', //自定义波形
      amplitudeModType: 0, //调幅类型
      modRate: 200e3, //调制率
      modDeepValue: 50 //调制深度
    }
    freqModulationForm.value = {
      enable: false,
      freqOne: 200e3, //频率一
      freqTwo: 200e3, //频率二
      freqTwoAP: 50, //频率2幅度占比
      freqModOrigin: 0, //调制源
      externalCoupMod: 0 //外部输入耦合方式
    }
    phaseModulationForm.value = {
      enable: false,
      userDefinedWaveform: false, //自定义波形开关
      waveformType: '', //标准波形
      userDefinedWaveformType: '', //自定义波形
      phaseModBw: 0, //调相带宽
      modRate: 200e3, //调制率
      phaseModOffset: 10 //调相相偏
    }
  }

  const updateValue = (key, form, newValue, unit) => {
    form[key] = plotToNum(newValue + unit) // 更新表单中的原始值
  }
  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  const handleChange = newActiveName => {
    console.log(newActiveName)
    reset()
  }

  const setFormData = data => {
    amplitudeModulationForm.value = { ...data.amplitudeModulation }
    freqModulationForm.value = { ...data.freqModulation }
    phaseModulationForm.value = { ...data.phaseModulation }
    if (data.amplitudeModulation?.enable) {
      activeName.value = 'AM'
    } else if (data.freqModulation?.enable) {
      activeName.value = 'FM'
    } else if (data.phaseModulation?.enable) {
      activeName.value = 'PM'
    } else {
      activeName.value = 'AM'
    }
  }

  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'waveform_type')
      .slice(0, -1) // 去除最后一项
      .forEach(item => {
        standardWaveformOptions.push({ label: item.dictLabel, value: item.dictValue })
      })

    await allWavePlan({ pageSize: 1, pageNum: 100 }).then(res => {
      res.data.list.forEach(item => {
        customWaveformOptions.push({ label: item.fileName, value: item.fileName })
      })
    })
  }

  onMounted(() => {
    getDictionaryData()
  })

  defineExpose({
    amplitudeModulationForm,
    freqModulationForm,
    phaseModulationForm,
    reset,
    setFormData
  })
</script>

<template>
  <el-tabs
    v-model="activeName"
    type="border-card"
    @tab-click="handleClick"
    @tab-change="handleChange"
    tab-position="left"
  >
    <el-tab-pane label="幅度调制" name="AM">
      <el-form :model="amplitudeModulationForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="amplitudeModulationForm.enable">启用</el-checkbox>
          <el-checkbox v-model="amplitudeModulationForm.modDeep">深度调幅</el-checkbox>
          <el-checkbox v-model="amplitudeModulationForm.userDefinedWaveform"
            >自定义波形</el-checkbox
          >
        </el-form-item>
        <el-form-item label="标准波形：">
          <el-select
            style="width: 200px"
            v-model="amplitudeModulationForm.waveformType"
            placeholder="请选择波形"
          >
            <el-option
              v-for="item in standardWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="自定义波形：" v-if="amplitudeModulationForm.userDefinedWaveform">
          <el-select
            style="width: 200px"
            v-model="amplitudeModulationForm.userDefinedWaveformType"
            placeholder="请选择波形"
          >
            <el-option
              v-for="item in customWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="调幅类型：">
          <el-radio-group
            v-model="amplitudeModulationForm.amplitudeModType"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="linear" :value="0">线性</el-radio-button>
            <el-radio-button class="cusBtn" label="index" :value="1">指数</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调制率：">
          <UnitCom
            :value="amplitudeModulationForm.modRate"
            @update:value="
              (newValue, unit) => updateValue('modRate', amplitudeModulationForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="调制深度：">
          <el-input style="width: 200px" v-model="amplitudeModulationForm.modDeepValue">
            <template #append> % </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="频率调制" name="FM">
      <el-form :model="freqModulationForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="freqModulationForm.enable">启用</el-checkbox>
        </el-form-item>
        <el-form-item label="频率1：">
          <UnitCom
            :value="freqModulationForm.freqOne"
            @update:value="
              (newValue, unit) => updateValue('freqOne', freqModulationForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="频率2：">
          <UnitCom
            :value="freqModulationForm.freqTwo"
            @update:value="
              (newValue, unit) => updateValue('freqTwo', freqModulationForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="频率2幅度占比：">
          <el-input style="width: 200px" v-model="freqModulationForm.freqTwoAP">
            <template #append> % </template>
          </el-input>
        </el-form-item>
        <el-form-item label="调频源：">
          <el-radio-group
            v-model="freqModulationForm.freqModOrigin"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="inner" :value="0">内部</el-radio-button>
            <el-radio-button class="cusBtn" label="outside" :value="1">外部</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="外部输入耦合方式：">
          <el-radio-group
            v-model="freqModulationForm.externalCoupMod"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="DC" :value="0">直流耦合</el-radio-button>
            <el-radio-button class="cusBtn" label="AC" :value="1">交流耦合</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="相位调制" name="PM">
      <el-form :model="phaseModulationForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="phaseModulationForm.enable">启用</el-checkbox>
          <el-checkbox v-model="phaseModulationForm.userDefinedWaveform">自定义波形</el-checkbox>
        </el-form-item>
        <el-form-item label="标准波形：">
          <el-select
            style="width: 200px"
            v-model="phaseModulationForm.waveformType"
            placeholder="请选择波形"
          >
            <el-option
              v-for="item in standardWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="自定义波形：" v-if="phaseModulationForm.userDefinedWaveform">
          <el-select
            style="width: 200px"
            v-model="phaseModulationForm.userDefinedWaveformType"
            placeholder="请选择波形"
          >
            <el-option
              v-for="item in customWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="调相带宽：">
          <el-radio-group
            v-model="phaseModulationForm.phaseModBw"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="normal" :value="0">正常</el-radio-button>
            <el-radio-button class="cusBtn" label="broadband" :value="1">宽带</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调制率：">
          <UnitCom
            :value="phaseModulationForm.modRate"
            @update:value="
              (newValue, unit) => updateValue('modRate', phaseModulationForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="调相相偏：">
          <el-input style="width: 200px" v-model="phaseModulationForm.phaseModOffset">
            <template #append> rad </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
