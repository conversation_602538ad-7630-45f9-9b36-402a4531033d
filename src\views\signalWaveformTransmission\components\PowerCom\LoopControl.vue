<script setup>
  const form = ref({
    alcLoopStatus: 0,
    searchMode: '',
    searchOutput: '',
    outputBlanking: 0
  })

  const reset = () => {
    form.value = {
      alcLoopStatus: 0,
      searchMode: '',
      searchOutput: '',
      outputBlanking: 0
    }
  }

  const setFormData = data => {
    form.value = { ...data }
  }

  defineExpose({
    form,
    reset,
    setFormData
  })
</script>

<template>
  <el-form :model="form" label-width="auto" label-position="left">
    <el-form-item label="ALC环路状态：">
      <el-radio-group v-model="form.alcLoopStatus" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="close" :value="0">闭环</el-radio-button>
        <el-radio-button class="cusBtn" label="open" :value="1">开环</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="搜索方式：">
      <el-radio-group
        :disabled="form.alcLoopStatus === 0"
        v-model="form.searchMode"
        size="large"
        style="width: 200px"
      >
        <el-radio-button class="cusBtn" label="manual" :value="0">手动</el-radio-button>
        <el-radio-button class="cusBtn" label="auto" :value="1">自动</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="搜索输出：">
      <el-radio-group
        :disabled="form.alcLoopStatus === 0"
        v-model="form.searchOutput"
        size="large"
        style="width: 200px"
      >
        <el-radio-button class="cusBtn" label="normal" :value="0">正常</el-radio-button>
        <el-radio-button class="cusBtn" label="mini" :value="1">最小</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="输出消隐：">
      <el-radio-group v-model="form.outputBlanking" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="on" :value="0">开</el-radio-button>
        <el-radio-button class="cusBtn" label="off" :value="1">关</el-radio-button>
      </el-radio-group>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtnDataSource) {
    width: 20%;
  }
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
