import { defineStore } from 'pinia'
import useTrainingStore from '../simulation/training.js'

const { global } = useTrainingStore()
export default defineStore('trainingFormStore', {
  state: () => {
    const { file } = useTrainingStore()
    return {
      // 信号文件参数
      fileCenterFreIn: 0,//信号文件中心频率
      fileDebugMode: 0,
      fileSamplingRate: 0,//信号文件采样率
      fileBitRate: 0,//信号文码速率
      fileIfbw: 0,//信号文件中频带宽
      iqNum: 0,//信号文件IQ点数
      iqReverse: false, // IQ 颠倒
      logarithm: false, // 取对数

      //下变频
      ddcFlag: false,//下变频启用标志
      downCenterFreIn: 0,//下变频中心频率

      // 信号分选
      isITU: true,//ITU参数测量启用标志
      isSignalSort: true,//信号分选启用标志
      isSpreading: false,//扩频检测启用标志

      // 参考线
      refLine: false,
      level: -80,


      // 重叠处理方式
      overlapType: 1,//重叠处理方式   1：平均值 2：最大值

      // 分析设置
      allLen: 0,
      pickCf: 0,//分析设置中心频率
      pickBw: 0,//分析设置中频带宽
      startRate: 0,//分析设置起始频率
      endRate: 0,//分析设置终止频率

      //信号选取
      allPointLen: 0,
      startPoint: 0,
      endPoint: 65535,

      // IQ抽取
      isIQExtraction: false,//抽取IQ点启用标志
      iqExtractionRatio: 3,//抽取比率

      // IQ截取
      isIQCut: false,//截取IQ点启用标志
      iqCutStartPoint: 0,//截取起始点
      iqCutLength: 0,//IQ长度

      // FFT点数
      fftPoint: 8192,//FFT点数

      amScale: 10,
      capital: false,
      currentFileParams: file,
      path: 0
    }
  },
  getters: {
    len() {
      return round(this.endPoint) - round(this.startPoint) + 1
    },
    bandwidth() {
      return this.endRate - this.startRate
    },
    centerFreqIn() {
      return global.centerFreqIn
    },
    debugMode() {
      return global.debugMode
    },
    samplingRate() {
      return global.samplingRate
    },
    bitRate() {
      return global.bitRate
    },
    intermediateFrequencyBandwidth() {
      return global.intermediateFrequencyBandwidth
    },
    refLevel() {
      return global.refLevel
    },
    scale() {
      if (this.fileIfbw) {
        return this.allLen / this.fileIfbw
      }
    },
    left() {
      return this.fileCenterFreIn - this.fileIfbw / 2
    }
  },
  actions: {
    init(bw, cf) {
      this.pickCf = cf * 1
      this.pickBw = bw * 1
      this.setRateByBw()
      // this.setPoint()
    },
    // 根据中心频率，中频带宽设置起始点
    setRateByBw() {
      this.startRate = this.pickCf - this.pickBw / 2
      this.endRate = this.pickCf + this.pickBw / 2
    },
    setRateByDDC() {
      if (this.ddcFlag) {
        this.startRate = this.downCenterFreIn - this.fileIfbw / 2
        this.endRate = Number(this.downCenterFreIn) + Number(this.fileIfbw) / 2
        this.pickCf = this.downCenterFreIn
        this.pickBw = this.fileIfbw
      }
    },
    setCfAndBwByRate() {
      const oldCenterFreIn = this.ddcFlag ? this.downCenterFreIn : this.fileCenterFreIn
      const oldStartRate = Number(oldCenterFreIn) - Number(this.fileIfbw) / 2
      const oldEndRate = Number(oldCenterFreIn) + Number(this.fileIfbw) / 2
      // 检查新的 startRate 和 endRate 是否满足条件
      if (this.startRate > oldEndRate || this.startRate < oldStartRate || this.endRate > oldEndRate || this.endRate < oldStartRate || this.startRate >= this.endRate) {
        ElMessage.error('输入的起始频率和结束频率不符合要求，请检查输入值。');
        this.startRate = oldStartRate
        this.endRate = oldEndRate
        // return;
      }
      this.pickBw = this.endRate - this.startRate
      this.pickCf = this.startRate + this.pickBw / 2
    },
    setRateByPoint() {
      if (!this.scale) {
        return
      }
      this.startRate = this.startPoint / this.scale + this.left
      this.endRate = (this.endPoint + 1) / this.scale + this.left
    },
    setPoint() {
      if (!this.scale) {
        return
      }
      this.startPoint = round(this.scale * (this.startRate - this.left))
      this.endPoint = round(this.scale * (this.endRate - this.left) - 1)
    },

    setFilterorder() {
      return this.filterorder
    },
    setCoefficientDown(val) {
      this.coefficientDown = parseFloat(val)
    },
    setCenterFre() {
      if (this.pickCf !== 0) {
        global.centerFreqIn = this.pickCf
      }
    },
    getFileParams() {
      return cloneDeep(this.currentFileParams)
    },
    setFileParams(data) {
      this.currentFileParams = data
    }
  }
})
