<script setup>
  import DBUnitCom from './DBUnitCom.vue'

  const form = ref({
    pw: -20, // 功率
    pwStep: 0.1, // 功率步进
    pwOffset: 0, // 功率偏置
    freqReference: 0, // 功率参考
    freqReferenceVlaue: 0, // 功率参考值
    pwLimit: 0, // 功率限定
    maxOutputPw: 0, // 最大输出功率
    userFlatnessCompensation: 0 // 用户平坦度补偿
  })

  const reset = () => {
    form.value = {
      pw: -20, // 功率
      pwStep: 0.1, // 功率步进
      pwOffset: 0, // 功率偏置
      freqReference: 0, // 功率参考
      freqReferenceVlaue: 0, // 功率参考值
      pwLimit: 0, // 功率限定
      maxOutputPw: 0, // 最大输出功率
      userFlatnessCompensation: 0 // 用户平坦度补偿
    }
  }

  const setFormData = data => {
    form.value = { ...data }
  }

  defineExpose({
    form,
    reset,
    setFormData
  })
</script>

<template>
  <el-form :model="form" label-width="auto" label-position="left">
    <el-form-item label="功率：">
      <!-- 显示转换后的值 -->
      <DBUnitCom :value="form.pw" @update:value="val => (form.pw = val)" />
    </el-form-item>
    <el-form-item label="功率步进：">
      <el-input style="width: 200px" v-model="form.pwStep">
        <template #append> dB </template>
      </el-input>
    </el-form-item>
    <el-form-item label="功率偏置：">
      <el-input style="width: 200px" v-model="form.pwOffset">
        <template #append> dB </template>
      </el-input>
    </el-form-item>
    <el-form-item label="功率参考：">
      <el-radio-group v-model="form.freqReference" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="manual" :value="0">手动</el-radio-button>
        <el-radio-button class="cusBtn" label="auto" :value="1">自动</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="功率参考值：">
      <el-input
        :disabled="form.freqReference === 1"
        style="width: 200px"
        v-model="form.freqReferenceVlaue"
      >
        <template #append> dBm </template>
      </el-input>
    </el-form-item>
    <el-form-item label="功率限定：">
      <el-radio-group v-model="form.pwLimit" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="manual" :value="0">手动</el-radio-button>
        <el-radio-button class="cusBtn" label="auto" :value="1">自动</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="最大输出功率：">
      <el-input :disabled="form.pwLimit === 1" style="width: 200px" v-model="form.maxOutputPw">
        <template #append> dBm </template>
      </el-input>
    </el-form-item>
    <el-form-item label="用户平坦度补偿：">
      <el-radio-group v-model="form.userFlatnessCompensation" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="on" :value="0">开</el-radio-button>
        <el-radio-button class="cusBtn" label="off" :value="1">关</el-radio-button>
      </el-radio-group>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtnDataSource) {
    width: 20%;
  }
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
