<script setup>
  import SignalSettings from './SignalSettings.vue'
  import AnalogModulation from './AnalogModulation.vue'

  const props = defineProps({
    formData: {
      type: Object,
      default: () => ({})
    }
  })

  const activeName = ref('SignalSettings')
  const SignalSettingsRef = ref(null)
  const AnalogModulationRef = ref(null)

  const handleClick = (tab, event) => {
    SignalSettingsRef.value.reset()
    AnalogModulationRef.value.reset()
  }

  watch(
    () => props.formData,
    newVal => {
      const analogModulationViewFlag = Object.values(newVal.analogModulation).some(item => {
        return item?.enable
      })
      const settingViewFlag = Object.values(newVal.setting).some(item => {
        return item?.enable
      })
      if (analogModulationViewFlag) {
        activeName.value = 'AnalogModulation'
      } else if (settingViewFlag) {
        activeName.value = 'SignalSettings'
      }
    },
    {
      deep: true
    }
  )

  defineExpose({
    SignalSettingsRef,
    AnalogModulationRef
  })
</script>

<template>
  <el-tabs class="h-full" v-model="activeName" type="border-card" @tab-click="handleClick">
    <el-tab-pane label="信号设置" name="SignalSettings">
      <SignalSettings ref="SignalSettingsRef" />
    </el-tab-pane>
    <el-tab-pane label="模拟调制" name="AnalogModulation">
      <AnalogModulation ref="AnalogModulationRef" />
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.el-tabs__nav) {
    float: none !important;
  }
  :deep(.el-tabs__nav .is-top) {
    width: 50% !important;
  }
</style>
