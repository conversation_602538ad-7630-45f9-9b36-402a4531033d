<script setup>
  import * as mtApi from '@/api/business/students'
  import useDictStore from '@/store/modules/dict'
  import useList from '@/api/tool/filemanage/tableFunctionPro.js'
  import { ElMessage } from 'element-plus'

  const props = defineProps({
    selectedStudents: {
      type: Array,
      default: () => []
    }
  })

  const dictStore = useDictStore()
  const emit = defineEmits(['UpwardFun'])
  const xTable = ref(null) // 表格实例
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const sysUserSexOptions = ref([]) // 性别字典数据
  const selectRows = ref(null)
  const filterOption = ref({
    stuName: '', // 学生姓名
    stuPhone: '', // 手机号码
    startTime: '', // 开始时间
    endTime: '' // 结束时间
  })

  const { list, loading, curPage, size, total, timeSearch, loadData } = useList(
    mtApi.listStudents,
    mtApi.delStudents,
    filterOption,
    xTable
  )

  const clearName = () => {
    filterOption.stuName = ''
    loadData()
  }

  const handleClose = () => {
    dialogTableVisible.value = false
  }

  // 勾选框事件
  const selectChangeEvent = () => {
    const selectRecords = xTable.value.getCheckboxRecords()
    selectRows.value = selectRecords
  }

  const getDictionaryData = async () => {
    const processDictionary = (dictType, targetOptions) => {
      dictStore.dict
        .filter(item => item.dictType === dictType)
        .forEach(item => {
          targetOptions.value.push({ label: item.dictLabel, value: item.dictValue })
        })
    }
    processDictionary('sys_user_sex', sysUserSexOptions)
  }

  const confirm = () => {
    if (!selectRows.value) {
      return ElMessage.error('请选择学员')
    }
    emit('UpwardFun', selectRows.value)
    dialogTableVisible.value = false
  }

  onMounted(async () => {
    await getDictionaryData()
  })
</script>

<template>
  <cu-dialog
    class="px-10"
    v-model="dialogTableVisible"
    title="任务概况"
    align="center"
    width="800"
    @confirm="confirm"
    @cancel="handleClose"
  >
    <CuTitle title="学员管理" />
    <vxe-toolbar class="vxe-toolbar">
      <template #buttons>
        <vxe-input
          v-model="filterOption.stuName"
          type="search"
          placeholder="请输入学生姓名"
          clearable
          @clear="clearName"
        />
        <vxe-button status="primary" content="查询" @click="timeSearch(filterOption)" />
      </template>
    </vxe-toolbar>
    <vxe-table
      ref="xTable"
      border
      stripe
      size="medium"
      height="380"
      :data="list"
      :loading="loading"
      :checkbox-config="{
        labelField: 'listId',
        checkMethod: ({ row }) => {
          return !selectedStudents.some(item => item.stuCode === row.stuCode)
        }
      }"
      :row-config="{ isCurrent: true, isHover: true }"
      @checkbox-change="selectChangeEvent"
      @checkbox-all="selectChangeEvent"
    >
      <vxe-column type="checkbox" title="序号" width="90" fixed="left" align="center" />
      <vxe-column
        field="stuCode"
        title="学生学号"
        width="200"
        fixed="left"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      />
      <vxe-column
        field="stuName"
        title="姓名"
        width="160"
        fixed="left"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      />
      <vxe-column field="stuSex" title="学生性别" width="100" align="center">
        <template #default="{ row }">
          {{ sysUserSexOptions.filter(item => item.value === row.stuSex)[0]?.label || '-' }}
        </template>
      </vxe-column>
      <vxe-column field="srClazz" title="所属期班" width="100" align="center">
        <template #default="{ row }">
          {{ row.srClazz?.claName || '-' }}
        </template>
      </vxe-column>
    </vxe-table>
    <!-- 分页 -->
    <p>
      <vxe-pager
        v-model:current-page="curPage"
        v-model:page-size="size"
        class="vxe-page"
        perfect
        :total="total"
        :page-sizes="[10, 20, 50, 100, 200, 500]"
        :layouts="[
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'Sizes',
          'FullJump',
          'Total'
        ]"
      />
    </p>
  </cu-dialog>
</template>
