<script setup>
  import { useRouter } from 'vue-router'
  import useList from '@/api/tool/filemanage/tableFunctionPro.js'
  import useDictStore from '@/store/modules/dict'
  import useTrainingStore from '@/store/modules/simulation/training.js'
  import {
    allTMList,
    deleteTM,
    addOrUpdateTM,
    taskAnswerDetail,
    exportTrainTask,
    changeTMStatus
  } from '@/api/simulation/trainingMission.js'
  import { selectOne } from '@/api/file/index.js'
  import TrainingMissionDialog from '../components/trainingMissionDialog/index.vue'
  import AnswersEditDialog from '../components/answersEditDialog/index.vue'
  import TrainingReportDialog from '../components/trainingReportDialog/index.vue'
  import { ElMessage } from 'element-plus'
  import { cloneDeep } from 'lodash'
  import { displayUnitConversion } from '@/api/tool/filemanage'
  import { downloadFile } from '@/utils/fileDownload.js'
  import { checkRole } from '@/utils/permission.js'

  const filterOption = ref({
    taskName: '', // 任务名称
    createUser: '', // 组训人
    startTime: '', // 开始时间
    endTime: '' // 结束时间
  })
  const dictStore = useDictStore()
  const trainingStore = useTrainingStore() // 训练任务 store
  const xTable = ref(null) // 表格实例
  const router = useRouter() // 路由实例
  const TMDialogFlag = ref(false) // 新增任务对话框标题
  const AEDialogFlag = ref(false) // 答案编辑对话框标题
  const TRDialogFlag = ref(false) // 训练报告对话框标题
  const taskStatusOptions = ref([]) // 任务状态 （0：未开始，1：已编辑答案，2：已发布，3：进行中，4：已完成，5：销毁）
  const trainStatusOptions = ref([]) // 训练状态（0：未训练，1：已训练，2：已评分，3：缺训）
  const TMId = ref(null) // 任务ID
  const reportData = ref({}) // 训练报告数据
  const {
    list,
    loading,
    curPage,
    size,
    total,
    timeSearch,
    loadData,
    deleteAll,
    selectChangeEvent
  } = useList(allTMList, deleteTM, filterOption, xTable)

  // 假设每个按钮的宽度约为 120px，额外留 10px 间距
  const actionColumnWidth = computed(() => {
    const buttonWidth = 120
    const extraSpacing = 10
    if (!list.value.length) {
      return buttonWidth + extraSpacing
    }
    // 缓存角色判断结果
    const teacher = checkRole(['teacher'])
    const student = checkRole(['students'])
    // 遍历所有行，计算每行显示的按钮数，取最大值
    const maxButtonCount = list.value.reduce((max, row) => {
      let visibleButtonCount = 0
      // 答案编辑按钮：仅当教师且任务状态既不为 '4' 也不为 '5'
      if (teacher && row.taskStatus !== '4' && row.taskStatus !== '5') {
        visibleButtonCount++
      }
      // 启动任务按钮：当教师且任务状态为 0 或 1
      if (teacher && [0, 1].includes(Number(row.taskStatus))) {
        visibleButtonCount++
      }
      // 结束任务按钮：当教师且任务状态为 '2' 或 '3'
      if (teacher && (row.taskStatus === '2' || row.taskStatus === '3')) {
        visibleButtonCount++
      }
      // 进入训练按钮：当学生且 hasFinished 为 '0'
      if (student && (row.hasFinished === '0' || !row.hasFinished)) {
        visibleButtonCount++
      }
      // 训练详情按钮总是显示
      visibleButtonCount++
      return Math.max(max, visibleButtonCount)
    }, 0)
    return maxButtonCount * buttonWidth + extraSpacing
  })

  // 缓存文件数据转化
  const transToStore = row => {
    const result = {}
    const requiredKeys = [
      'id',
      'samplingRate',
      'bitRate',
      'centerFreqIn',
      'dataType',
      'debugMode',
      'fftSize',
      'fileType',
      'intermediateFrequencyBandwidth',
      'uploadTime',
      'iqReverse',
      'logarithm',
      'startOffset',
      'startOffLength',
      'cutoffLength',
      'steplen',
      'powerMultiple'
    ]
    Object.keys(row).forEach(key => {
      if (!requiredKeys.includes(key)) {
        return
      }
      if (typeof row[key] === 'string' && row[key].includes('Hz')) {
        result[key] = displayUnitConversion(row[key])
      } else {
        result[key] = row[key]
      }
    })
    result.fileName = row.fileName
    return result
  }

  const clearName = () => {
    filterOption.taskName = ''
    timeSearch(filterOption.value)
  }

  const clearUser = () => {
    filterOption.createUser = ''
    timeSearch(filterOption.value)
  }

  /**
   * 新增任务
   */
  const addMission = () => {
    TMDialogFlag.value = true
  }

  const editClick = row => {
    TMId.value = row.id
    AEDialogFlag.value = true
  }

  /**
   *  新增或修改任务
   */
  const addOrUpdate = ({ form, trainerList }) => {
    const params = {
      id: form.id,
      taskName: form.taskName,
      taskDescribe: form.taskDescribe,
      startTime: form.startTime,
      examDuration: form.examDuration,
      taskFileId: form.fileId,
      taskFile: form.fileName,
      stuCodeList: trainerList // 组训人列表 id
    }
    addOrUpdateTM(params).then(res => {
      if (res.code === 200) {
        loadData()
        ElMessage.success(res.msg)
      } else {
        ElMessage.error(res.msg)
      }
    })
  }

  // 导出训练任务数据
  const exportFile = async () => {
    await exportTrainTask().then(res => {
      downloadFile(res, '训练任务')
    })
  }

  /**
   * 修改任务状态
   * @param row 当前行数据
   * @param type 1：启用 2：结束
   */
  const changeTaskStatus = (row, type) => {
    const params = {
      ...cloneDeep(row),
      taskStatus: type
    }
    changeTMStatus(params).then(res => {
      if (res.code === 200) {
        ElMessage.success(res.msg)
        loadData()
      }
    })
  }

  /**
   * 跳转训练识别页面
   * @param row 当前行数据
   */
  const handleClick = async row => {
    await selectOne({ id: row.taskFileId }).then(res => {
      trainingStore.setFileInfo(transToStore(res.data), '0')
      trainingStore.writeStorage()
    })
    router.push({ name: 'RecognitionTraining', query: { taskId: row.id } })
  }

  /**
   *  查看训练详情数据
   * @param row 当前行数据
   */
  const trainReportFun = async row => {
    await taskAnswerDetail({ taskId: row.id }).then(res => {
      if (res.code === 200) {
        reportData.value = res.data
        TRDialogFlag.value = true
      }
    })
  }

  // 答案编辑按钮：仅当教师且任务状态既不为 '4' 也不为 '5'
  function canEditAnswer(row) {
    return row.taskStatus !== '4' && row.taskStatus !== '5'
  }

  // 启动任务按钮：教师角色时显示，当任务状态为 0 或 1
  function canStartTask(row) {
    return [0, 1].includes(Number(row.taskStatus))
  }
  // 并且当任务状态为 0 时禁用启动按钮
  function isStartDisabled(row) {
    return Number(row.taskStatus) === 0
  }

  // 结束任务按钮：教师角色时显示，当任务状态为 '2' 或 '3'
  function canEndTask(row) {
    return row.taskStatus === '2' || row.taskStatus === '3'
  }

  // 进入训练按钮：对于学生，当 hasFinished 为 '2' 或 '3' 或任务状态为 '4' 或 '5' 时禁用
  function isEnterDisabled(row) {
    return (
      row.hasFinished === '2' ||
      row.hasFinished === '3' ||
      row.hasFinished === '4' ||
      row.taskStatus === '4' ||
      row.taskStatus === '5'
    )
  }

  //  获取 任务状态 字典数据
  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'task_status')
      .forEach(item => {
        taskStatusOptions.value.push({ label: item.dictLabel, value: item.dictValue })
      })
    dictStore.dict
      .filter(item => item.dictType === 'train_status')
      .forEach(item => {
        trainStatusOptions.value.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  onMounted(async () => {
    await getDictionaryData()
  })
</script>
<template>
  <CuTitle title="训练任务" />
  <vxe-toolbar class="vxe-toolbar">
    <template #buttons>
      <vxe-input
        v-model="filterOption.taskName"
        type="search"
        placeholder="请输入任务名称"
        clearable
        @clear="clearName"
        @search-click="timeSearch(filterOption)"
      />
      <vxe-input
        v-model="filterOption.createUser"
        type="search"
        placeholder="请输入组训人"
        clearable
        @clear="clearUser"
        @search-click="timeSearch(filterOption)"
      />
      <vxe-input
        v-model="filterOption.startTime"
        type="date"
        placeholder="请选择训练开始时间"
        class="time-button"
        clearable
      />
      <cu-button content="查询" @click="timeSearch(filterOption)" />
      <cu-button v-hasRole="['teacher']" content="新增" @click="addMission" />
      <cu-button content="导出" @click="exportFile" />
      <cu-button v-hasRole="['teacher']" content="删除" @click="deleteAll" />
    </template>
  </vxe-toolbar>
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    height="680"
    :data="list"
    :loading="loading"
    :checkbox-config="{ labelField: 'listId' }"
    :row-config="{ isCurrent: true, isHover: true }"
    @checkbox-change="selectChangeEvent"
    @checkbox-all="selectChangeEvent"
  >
    <vxe-column type="checkbox" title="序号" width="90" fixed="left" align="center" />
    <vxe-column
      field="taskName"
      title="任务名称"
      width="140"
      fixed="left"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="taskDescribe"
      title="任务描述"
      width="160"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      v-hasRole="['teacher']"
      field="taskStatus"
      title="任务状态"
      width="120"
      align="center"
    >
      <template #default="{ row }">
        {{ taskStatusOptions.filter(item => item.value === row.taskStatus)[0]?.label || '-' }}
      </template>
    </vxe-column>
    <vxe-column
      v-hasRole="['students']"
      field="hasFinished"
      title="训练状态"
      width="120"
      align="center"
    >
      <template #default="{ row }">
        {{ trainStatusOptions.filter(item => item.value === row.hasFinished)[0]?.label || '-' }}
      </template>
    </vxe-column>
    <vxe-column field="createTime" title="创建时间" width="180" align="center" />
    <vxe-column field="startTime" title="训练起始时间" width="180" align="center" />
    <vxe-column field="examDuration" title="训练时长（min）" width="130" align="center" />
    <vxe-column field="createUser" title="组训人" width="100" align="center" />
    <vxe-column
      v-hasRole="['teacher']"
      field="trainCount"
      title="应参训人数"
      width="100"
      align="center"
    />
    <vxe-column
      v-hasRole="['teacher']"
      field="trainEndCount"
      title="已完成人数"
      width="100"
      align="center"
    />
    <vxe-column
      v-hasRole="['teacher']"
      field="avgScore"
      title="平均得分"
      width="100"
      align="center"
    />
    <vxe-column
      field="taskFile"
      title="识别文件"
      width="160"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column field="操作" title="操作" :width="actionColumnWidth" fixed="right" align="center">
      <template #default="{ row }">
        <cu-button
          v-hasRole="['teacher']"
          v-show="canEditAnswer(row)"
          content="答案编辑"
          @click="editClick(row)"
        />
        <cu-button
          v-hasRole="['teacher']"
          v-show="canStartTask(row)"
          :disabled="isStartDisabled(row)"
          content="启动任务"
          @click="changeTaskStatus(row, '2')"
        />
        <cu-button
          v-hasRole="['teacher']"
          v-show="canEndTask(row)"
          content="结束任务"
          @click="changeTaskStatus(row, '5')"
        />
        <cu-button
          v-hasRole="['students']"
          content="进入训练"
          :disabled="isEnterDisabled(row)"
          @click="handleClick(row)"
        />
        <cu-button v-hasRole="['teacher']" content="训练详情" @click="trainReportFun(row)" />
      </template>
    </vxe-column>
  </vxe-table>
  <!-- 分页 -->
  <p>
    <vxe-pager
      v-model:current-page="curPage"
      v-model:page-size="size"
      class="vxe-page"
      perfect
      :total="total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total'
      ]"
    />
  </p>

  <TrainingMissionDialog v-if="TMDialogFlag" v-model="TMDialogFlag" @submit="addOrUpdate" />
  <AnswersEditDialog
    v-if="AEDialogFlag"
    v-model="AEDialogFlag"
    :id="TMId"
    @reload-list="loadData()"
  />
  <TrainingReportDialog v-if="TRDialogFlag" v-model="TRDialogFlag" :report-data="reportData" />
</template>

<style lang="scss" scoped>
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }
</style>
