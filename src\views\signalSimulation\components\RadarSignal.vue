<script setup>
    import { plotToNum } from '@/utils/utils'
    import UnitCom from '@/components/UnitCom/index.vue'
    import useDictStore from '@/store/modules/dict'
    import { modulationTypeOptions } from '@/constant/simulation.js'

    const dictStore = useDictStore()
    const activeName = ref('PulseSettings') //默认展示调制参数
    const filterTypeOptions = reactive([])
    const dataSourceOptions = reactive([])

    // 脉冲设置
    const pulseSettingsForm = ref({
      priModulationMode: 'Sliding',
      pulseWidth: 1,
      priBase: 1,
      priStep: 1,
      priProgressMode: 0,
      priInterleavingScheme: '',
      priMaxDeviation: 0,
      priJitterDeviation: 0
    })

    // 扫描设置
    const dataSourceForm = ref({})

    // 码片设置
    const filterForm = ref({})


    const visible = computed(() => {
    const mode = pulseSettingsForm.value.priModulationMode
    return {
      pulseWidth:                   // 脉宽：所有模式都有
        ['Sliding','Constant','Stagger','Triangle','Sine','Jittered'].includes(mode),
      priBase:                      // 基准：除 Stagger 外都有
        ['Sliding','Constant','Triangle','Sine','Jittered'].includes(mode),
      priStep:                      // 步长：仅 Sliding, Triangle
        ['Sliding','Triangle'].includes(mode),
      priProgressMode:              // 渐进方式：Sliding, Triangle, Sine
        ['Sliding','Triangle','Sine'].includes(mode),
      priInterleavingScheme:        // 交错方案：仅 Stagger
        mode === 'Stagger',
      priMaxDeviation:              // PRI 最大偏差：仅 Sine
        mode === 'Sine',
      priJitterDeviation,           // PRI 抖动偏差：仅 Jittered
        mode === 'Jittered',
    }
  })

    const reset = () => {
      pulseSettingsForm.value = {}
      dataSourceForm.value = {}
      filterForm.value = {}
    }

    const updateValue = (key, form, newValue, unit) => {
      form[key] = plotToNum(newValue + unit) // 更新表单中的原始值
    }
    const handleClick = (tab, event) => {
      console.log(tab, event)
    }

    const handleChange = newActiveName => {
      console.log(newActiveName)
    }

    const setFormData = data => {}

    const getDictionaryData = async () => {
      dictStore.dict
        .filter(item => item.dictType === 'digital_mod_filter')
        .forEach(item => {
          filterTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
        })

      dictStore.dict
        .filter(item => item.dictType === 'digital_mod_data_source')
        .forEach(item => {
          dataSourceOptions.push({ label: item.dictLabel, value: item.dictValue })
        })
    }

    onMounted(() => {
      getDictionaryData()
    })

    defineExpose({
      pulseSettingsForm,
      dataSourceForm,
      filterForm,
      reset,
      setFormData
    })

    const handleChangeModulationType = val => {
      console.log(val)
    }
</script>

<template>
  <el-tabs
    v-model="activeName"
    type="border-card"
    @tab-click="handleClick"
    @tab-change="handleChange"
    tab-position="top"
  >
    <el-tab-pane label="脉冲设置" name="PulseSettings">
      <el-form :model="pulseSettingsForm" label-width="160px" label-position="left">
        <el-row :gutter="24">
          <!-- 第一列 -->
          <el-col :span="12">
            <el-form-item label="PRI调制模式：">
              <el-select
                style="width: 200px"
                v-model="pulseSettingsForm.priModulationMode"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in modulationTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="PRI渐进方式：">
              <el-radio-group v-model="pulseSettingsForm.priProgressMode" size="large">
                <el-radio-button class="cusBtn" label="递增" :value="0">递增</el-radio-button>
                <el-radio-button class="cusBtn" label="递减" :value="1">递减</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <!-- 第二列 -->
          <el-col :span="12">
            <el-form-item label="脉宽：">
              <el-input-number v-model="pulseSettingsForm.pulseWidth" style="width: 200px">
                <template #suffix>us</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交错方案：">
              <el-input v-model="pulseSettingsForm.priInterleavingScheme" style="width: 200px" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="PRI基准：">
              <el-input-number v-model="pulseSettingsForm.priBase" style="width: 200px">
                <template #suffix>us</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="PRI最大偏差：">
              <el-input-number v-model="pulseSettingsForm.priMaxDeviation" style="width: 200px">
                <template #suffix>us</template>
              </el-input-number>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="PRI步长：">
              <el-input-number v-model="pulseSettingsForm.priStep" style="width: 200px">
                <template #suffix>us</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="PRI抖动偏差：">
              <el-input-number v-model="pulseSettingsForm.priJitterDeviation" style="width: 200px">
                <template #suffix>us</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-tab-pane>

    <!-- <el-tab-pane label="数据源" name="DataSource">
      <el-form :model="dataSourceForm" label-width="90" label-position="left">
        <el-form-item label="数据源：">
          <el-select
            style="width: 200px"
            v-model="dataSourceForm.dataSourceType"
            placeholder="请选择数据源"
          >
            <el-option
              v-for="item in dataSourceOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-tab-pane> -->

    <!-- <el-tab-pane label="滤波器" name="Filters">
      <el-form :model="filterForm" label-width="90" label-position="left">
        <el-form-item label="滤波器：">
          <el-select
            style="width: 200px"
            v-model="filterForm.filterType"
            placeholder="请选择滤波器"
          >
            <el-option
              v-for="item in filterTypeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Alpha/BT：">
          <el-input-number style="width: 200px" v-model="filterForm.phaseModOffset">
          </el-input-number>
        </el-form-item>
      </el-form>
    </el-tab-pane> -->
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }

  :deep(.el-tabs__nav) {
    float: none !important;
  }
  :deep(.el-tabs__nav .is-top) {
    width: 33.3% !important;
  }
</style>
