<script setup>
  import * as echarts from 'echarts'
  import { getStuTRStatisticDetail } from '@/api/simulation/trainingRecord.js'
  import useDictStore from '@/store/modules/dict'

  // 定义 props 接收父组件数据
  const props = defineProps({
    rowId: {
      type: String,
      default: ''
    }
  })

  const dictStore = useDictStore()
  // 对话框状态
  const dialogVisible = defineModel({ type: Boolean, default: false })

  const chartContainer = ref(null)
  let chartInstance = null
  const signalTypeOptions = reactive([])
  const apiData = ref([]) // 新增数据存储

  const chartOption = computed(() => {
    const categories = signalTypeOptions.map(opt => opt.label)
    const avgScores = []
    const maxScores = []
    signalTypeOptions.forEach(opt => {
      const matched = apiData.value.find(item => item.sigType === opt.value)
      avgScores.push(matched?.avgScore || 0)
      maxScores.push(matched?.maxScore || 0)
    })
    return {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      legend: {
        data: ['平均分', '最高分']
      },
      // grid: 调整图表四周的留白空间
      grid: {
        left: 60,
        right: 30,
        bottom: 100, // 让横坐标有更多空间显示标签
        top: 40
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisTick: { alignWithLabel: true },
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 45, // 旋转45度
          align: 'right', // 对齐方式，可根据需要设置 'center' 或 'right'
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: { formatter: '{value} 分' },
        min: 0,
        interval: 10
      },
      // dataZoom: 启用滚动，可左右拖拽查看
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0, // 默认显示区间的起始位置(0-100)
          end: 50 // 可根据实际数据量调整
        }
      ],
      series: [
        {
          name: '平均分',
          type: 'bar',
          barWidth: '35%',
          data: avgScores,
          label: { show: true, position: 'top' }
        },
        {
          name: '最高分',
          type: 'bar',
          barWidth: '35%',
          data: maxScores,
          label: { show: true, position: 'top' }
        }
      ]
    }
  })

  // 初始化图表
  const initChart = () => {
    if (!chartContainer.value) return
    // 如果已有实例则 dispose，防止重复初始化
    if (chartInstance) {
      chartInstance.dispose()
    }
    chartInstance = echarts.init(chartContainer.value)
    chartInstance.setOption(chartOption.value)
  }

  onBeforeMount(() => {
    dictStore.dict
      .filter(item => item.dictType === 'signal_type')
      .forEach(item => {
        signalTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  })

  onMounted(() => {
    getStuTRStatisticDetail({
      trainUser: props.rowId
    }).then(res => {
      apiData.value = Array.isArray(res.data) ? res.data : [res.data] // 处理接口返回数据
      nextTick(() => {
        initChart()
      })
    })
  })
</script>

<template>
  <cu-dialog
    v-model="dialogVisible"
    title="训练详情"
    width="1000"
    @confirm="dialogVisible = false"
    @cancel="dialogVisible = false"
  >
    <div ref="chartContainer" style="width: 100%; height: 480px; margin: 0 auto"></div>
  </cu-dialog>
</template>

<style scoped></style>
