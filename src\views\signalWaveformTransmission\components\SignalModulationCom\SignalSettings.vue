<script setup>
  import { plotToNum } from '@/utils/utils'
  import UnitCom from '@/components/UnitCom/index.vue'
  import useDictStore from '@/store/modules/dict'
  import { allSignalListGet } from '@/api/singalManage'

  const dictStore = useDictStore()
  const activeName = ref('RealTimeBaseBand') // 默认激活实时基带
  const debugModeList = ref([])
  const list = ref([])
  const curPage = ref(1)
  const size = ref(10)
  const total = ref(0)
  const fileForm = ref({
    signalType: '',
    modulationType: '',
    signalSystem: '0',
    fileName: ''
  })
  //实时基带
  const realTimeBaseBandForm = ref({
    enable: false,
    dataOrigin: 0, //数据源
    bitRate: 200e3, //码速率
    modType: '', //调制样式
    freqOffset: 200, //频偏
    phasePolarity: 0, //相位极性
    differentialEncod: 0, //差分编码
    filterType: '', //滤波器类型
    filterOrde: 0.3 //滤波器阶数
  })

  //双音
  const dualToneForm = ref({
    enable: false,
    amplitudeModType: 0, //调幅类型
    freqSpacing: 200e3 //频率间隔
  })

  // 多音
  const multiToneForm = ref({
    enable: false,
    audioNum: 2, //多音个数
    freqSpacing: 200e3, //频率间隔
    initPhase: 0, //初始相位
    tonePhase: 0, //音间相位
    modDeep: 7 //调制深度
  })

  //数据源枚举
  const dataSourceOptions = [
    { label: 'fileStream', value: 0, text: '文件码流' },
    { label: '0', value: 1, text: '全0' },
    { label: '1', value: 2, text: '全1' },
    { label: 'FRBS', value: 3, text: 'FRBS' },
    { label: 'custom', value: 4, text: '自定义序列' }
  ]
  const modulationModeOptions = reactive([]) //调制样式
  const filterOptions = [{ label: '根奈奎斯特滤波器', value: '0' }] //滤波器
  const fileSignalTypeOptions = [
    { label: '数字信号', value: '0' },
    { label: '模拟信号', value: '1' },
    { label: '脉冲信号', value: '2' },
    { label: '其他', value: '3' }
  ]
  const fileModulationTypeOptions = [
    { label: 'AIS', value: '0' },
    { label: 'ADS_B', value: '1' },
    { label: 'LINK4A', value: '2' },
    { label: '3GALE', value: '3' },
    { label: '塔康', value: '4' },
    { label: '导航', value: '5' },
    { label: '移动通信', value: '6' },
    { label: '民用广播', value: '7' },
    { label: '5g', value: '8' },
    { label: '其他', value: '9' }
  ]
  const fileSignalSystemOptions = [
    { label: '实测信号', value: '0' },
    { label: '模拟信号', value: '1' }
  ]
  // 重置表单
  const reset = () => {
    realTimeBaseBandForm.value = {
      enable: false,
      dataOrigin: 0, //数据源
      bitRate: 200e3, //码速率
      modType: '', //调制样式
      freqOffset: 200, //频偏
      phasePolarity: 0, //相位极性
      differentialEncod: 0, //差分编码
      filterType: '', //滤波器类型
      filterOrde: 0.3 //滤波器阶数
    }
    dualToneForm.value = {
      enable: false,
      amplitudeModType: 0, //调幅类型
      freqSpacing: 200e3 //频率间隔
    }
    multiToneForm.value = {
      enable: false,
      audioNum: 2, //多音个数
      freqSpacing: 200e3, //频率间隔
      initPhase: 0, //初始相位
      tonePhase: 0, //音间相位
      modDeep: 7 //调制深度
    }
  }

  const updateValue = (key, form, newValue, unit) => {
    console.log(key, newValue, unit)
    form[key] = plotToNum(newValue + unit) // 更新表单中的原始值
    console.log(form[key])
  }
  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  const handleChange = newActiveName => {
    console.log(newActiveName)
    reset()
  }

  /**
   * @description: 选择文件
   */
  const selectFile = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.onchange = () => {
      realTimeBaseBandForm.value.fileName = input.files[0].name
    }
    input.click()
  }

  const sendToDevice = () => {
    console.log('发送数据到设备')
  }

  const handleSearch = async () => {
    if (fileForm.value.signalSystem === '0') {
      const params = {
        pageNum: curPage.value,
        pageSize: size.value
      }
      await allSignalListGet(params).then(res => {
        if (res.code === 200) {
          list.value = res.data.rows
          list.value.forEach((item, index) => {
            item.listId = index + 1
          })
          total.value = res.data.total
        } else {
          // ElMessage.error(res.msg)
        }
      })
    } else {
      list.value = []
      total.value = 0
    }
  }

  const setFormData = data => {
    realTimeBaseBandForm.value = { ...data.realTimeBaseBand }
    dualToneForm.value = { ...data.dualTone }
    multiToneForm.value = { ...data.multiTone }
    if (data.realTimeBaseBand?.enable) {
      activeName.value = 'RealTimeBaseBand'
    } else if (data.dualTone?.enable) {
      activeName.value = 'DualTone'
    } else if (data.multiTone?.enable) {
      activeName.value = 'Polyphony'
    }
  }

  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'modulation_type')
      .forEach(item => {
        modulationModeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  const formatEnumFun = (value, enumList) => {
    let txtArr = enumList.filter(item => {
      return value == item.value || value == item.label
    })
    return txtArr.length > 0 ? txtArr[0].label : '-'
  }

  onMounted(() => {
    getDictionaryData()
  })

  defineExpose({
    realTimeBaseBandForm,
    dualToneForm,
    multiToneForm,
    reset,
    setFormData
  })
</script>

<template>
  <el-tabs
    v-model="activeName"
    type="border-card"
    @tab-click="handleClick"
    @tab-change="handleChange"
    tab-position="left"
  >
    <el-tab-pane label="实时基带" name="RealTimeBaseBand">
      <el-form :model="realTimeBaseBandForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="realTimeBaseBandForm.enable">启用</el-checkbox>
        </el-form-item>
        <el-form-item style="align-items: flex-start" label="数据源：">
          <div class="flex flex-col">
            <!-- 数据源单选框 -->
            <el-radio-group
              v-model="realTimeBaseBandForm.dataOrigin"
              size="large"
              style="width: 480px"
              class="data-source-group"
            >
              <el-radio-button
                v-for="item in dataSourceOptions"
                :key="item.label"
                :label="item.label"
                :value="item.value"
                class="cusBtnDataSource"
              >
                {{ item.text }}
              </el-radio-button>
            </el-radio-group>

            <!-- 文件选择输入框 -->
            <div class="flex items-center mt-4">
              <el-input v-model="realTimeBaseBandForm.fileName" style="width: 400px"></el-input>
              <cu-button content="浏览" class="ml-4" @click="selectFile" />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="码速率：">
          <UnitCom
            :value="realTimeBaseBandForm.bitRate"
            :unit-options="['kbps', 'Mbps']"
            @update:value="
              (newValue, unit) => updateValue('bitRate', realTimeBaseBandForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="调制样式：">
          <el-select
            style="width: 200px; margin-right: 20px"
            v-model="realTimeBaseBandForm.modType"
          >
            <el-option
              v-for="item in modulationModeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
          <div class="flex items-center">
            <span class="mr-2 w-[50px]">频偏</span>
            <el-input style="width: 200px" v-model="realTimeBaseBandForm.freqOffset">
              <template #append> kHz </template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="相位极性：">
          <el-radio-group
            v-model="realTimeBaseBandForm.phasePolarity"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="normal" :value="0">正常</el-radio-button>
            <el-radio-button class="cusBtn" label="revert" :value="1">翻转</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="差分编码：">
          <el-radio-group
            v-model="realTimeBaseBandForm.differentialEncod"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="on" :value="0">开</el-radio-button>
            <el-radio-button class="cusBtn" label="off" :value="1">关</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="滤波器：">
          <el-select
            style="width: 200px; margin-right: 20px"
            v-model="realTimeBaseBandForm.filterType"
          >
            <el-option
              v-for="item in filterOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
          <div class="flex items-center">
            <span class="mr-2">α</span>
            <el-input v-model="realTimeBaseBandForm.filterOrde"> </el-input>
          </div>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="双音" name="DualTone">
      <el-form :model="dualToneForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="dualToneForm.enable">启用</el-checkbox>
        </el-form-item>
        <el-form-item label="调幅类型：">
          <el-radio-group v-model="dualToneForm.amplitudeModType" size="large" style="width: 200px">
            <el-radio-button class="cusBtn" label="linear" :value="0">线性</el-radio-button>
            <el-radio-button class="cusBtn" label="index" :value="1">指数</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="频率间隔：">
          <UnitCom
            :value="dualToneForm.freqSpacing"
            @update:value="
              (newValue, unit) => updateValue('freqSpacing', dualToneForm, newValue, unit)
            "
          />
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="多音" name="Polyphony">
      <el-form :model="multiToneForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="multiToneForm.enable">启用</el-checkbox>
        </el-form-item>

        <el-form-item label="音频个数：">
          <el-input style="width: 200px" v-model="multiToneForm.audioNum"> </el-input>
        </el-form-item>
        <el-form-item label="频率间隔：">
          <UnitCom
            :value="multiToneForm.freqSpacing"
            @update:value="
              (newValue, unit) => updateValue('freqSpacing', multiToneForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="初始相位：">
          <el-radio-group v-model="multiToneForm.initPhase" size="large" style="width: 200px">
            <el-radio-button class="cusBtn" label="fixed" :value="0">固定</el-radio-button>
            <el-radio-button class="cusBtn" label="random" :value="1">随机</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="音间相位：">
          <el-radio-group v-model="multiToneForm.tonePhase" size="large" style="width: 200px">
            <el-radio-button class="cusBtn" label="fixed" :value="0">固定</el-radio-button>
            <el-radio-button class="cusBtn" label="random" :value="1">随机</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调制深度：">
          <el-input style="width: 200px" v-model="multiToneForm.modDeep">
            <template #append> deg </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="信号文件" name="SignalFile">
      <vxe-toolbar class="vxe-toolbar">
        <template #buttons>
          <vxe-select v-model="fileForm.signalSystem">
            <vxe-option
              v-for="item in fileSignalSystemOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </vxe-select>
          <vxe-select v-model="fileForm.modulationType">
            <vxe-option
              v-for="item in fileModulationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </vxe-select>
          <vxe-select v-model="fileForm.signalType">
            <vxe-option
              v-for="item in fileSignalTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </vxe-select>
          <vxe-input
            v-model="fileForm.fileName"
            type="search"
            placeholder="请输入文件名称"
            clearable
          />
          <cu-button content="查询" @click="handleSearch" />
        </template>
      </vxe-toolbar>
      <vxe-table
        ref="xTable"
        border
        stripe
        size="medium"
        height="380"
        :data="list"
        :row-config="{ isCurrent: true, isHover: true }"
      >
        <vxe-column type="seq" title="序号" width="90" fixed="left" align="center" />
        <vxe-column
          field="fileName"
          title="文件名称"
          width="140"
          fixed="left"
          align="center"
          show-header-overflow
          show-overflow="title"
          show-footer-overflow
        />
        <vxe-column field="taskDescribe" title="信号体制" width="100" align="center" />
        <vxe-column field="createTime" title="调制样式" width="100" align="center">
          <template #default="{ row }">
            <span>{{ formatEnumFun(row.debugMode, debugModeList) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="startTime" title="码速率" width="100" align="center" />
        <vxe-column
          field="intermediateFrequencyBandwidth"
          title="信号带宽"
          width="80"
          align="center"
        />
        <vxe-column field="操作" title="操作" width="80" fixed="right" align="center">
          <template #default="{ row }">
            <cu-button size="mini" content="发送至设备" @click="sendToDevice(row)" />
          </template>
        </vxe-column>
      </vxe-table>
      <p>
        <vxe-pager
          v-model:current-page="curPage"
          v-model:page-size="size"
          class="vxe-page"
          perfect
          :total="total"
          :page-sizes="[10, 20, 50, 100, 200, 500]"
          :layouts="[
            'PrevJump',
            'PrevPage',
            'Number',
            'NextPage',
            'NextJump',
            'Sizes',
            'FullJump',
            'Total'
          ]"
        />
      </p>
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.cusBtnDataSource) {
    width: 20%;
  }
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }
</style>
