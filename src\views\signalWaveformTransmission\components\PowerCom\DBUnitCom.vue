<script setup>
  import { ref, watch } from 'vue'

  // 接收父组件传来的“真实值（如 dBm）”
  const props = defineProps({
    value: {
      type: Number,
      default: -20
    },
    unitOptions: {
      type: Array,
      default: () => ['dBm', 'dBuv', 'nV', 'uV', 'mV', 'V']
    }
  })

  const emit = defineEmits(['update:value'])

  // 当前单位，默认是第一个单位
  const unit = ref(props.unitOptions[0])

  // 展示用的值（用户看到和编辑的内容，不随单位改变）
  const displayValue = ref('')

  // ===== 换算函数：从 displayValue + unit 得到统一单位值（如 dBm） =====
  function convertToDbm(value, unit) {
    const v = Number(value)
    if (isNaN(v)) return 0
    switch (unit) {
      case 'dBm':
        return v
      case 'dBuv':
        return 10 * Math.log10(Math.pow(v * 1e-6, 2) / 50)
      case 'nV':
        return 10 * Math.log10(Math.pow(v * 1e-9, 2) / 50)
      case 'uV':
        return 10 * Math.log10(Math.pow(v * 1e-6, 2) / 50)
      case 'mV':
        return 10 * Math.log10(Math.pow(v * 1e-3, 2) / 50)
      case 'V':
        return 10 * Math.log10(Math.pow(v, 2) / 50)
      default:
        return v
    }
  }

  // 初次加载时，设定展示值为传入的真实值（不做反向换算）
  watch(
    () => props.value,
    val => {
      if (displayValue.value === '') {
        displayValue.value = val.toString()
      }
    },
    { immediate: true }
  )

  // 用户切换单位：不改变 displayValue，只重新换算真实值
  function onUnitChange(newUnit) {
    unit.value = newUnit
    const dbmValue = convertToDbm(displayValue.value, unit.value)
    console.log('newUnit:', newUnit, 'dbmValue:', dbmValue)
    emit('update:value', dbmValue)
  }

  // 用户输入值变化：按当前单位换算真实值并 emit
  function onInputChange() {
    if (displayValue.value === '') return
    const dbmValue = convertToDbm(displayValue.value, unit.value)
    emit('update:value', dbmValue)
  }
</script>

<template>
  <el-input style="width: 200px" v-model="displayValue" @input="onInputChange" />
  <el-radio-group v-model="unit" size="large" class="ml-5" @change="onUnitChange">
    <el-radio-button v-for="item in unitOptions" :key="item" :label="item" :value="item" />
  </el-radio-group>
</template>
