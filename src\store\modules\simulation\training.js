import { defineStore } from 'pinia'
import { cloneDeep, round } from 'lodash'
import { storage } from '@/utils/Storage.ts'
import { queryFileFft } from '@/api/charts'
import operation from '@/common/operation'
import usePlayControl from '../playControl'

const STORAGE_KEY = 'trainingStorage'
const STORE_KEY = 'trainingStore'

const useTrainingStore = defineStore(STORE_KEY, {
  state: () => ({
    mode: 'dark', // light dark
    isLoadedLast: false, // 是否加载了最后一帧
    start: 0, // 在循环中的位置
    sequential: {
      value: null,
      currentRequest: null,
      query: null
    },
    settings: {
      step: 512, // 一次前进多少
      IQNum: 32768, // IQ点数
      simpleRate: 50 * 1e6, // 采样率 默认50M
      swpTime: 50, // 扫描时间，单位 us
      playbackRate: 1000 // 定时执行时间 单位ms
    },
    global: {
      refLevel: -20,
      samplingRate: 50000000,
      swpTime: 50, // 扫描时间，单位 us
      bitRate: 500000000,
      centerFreqIn: 0, // 中心频率
      dataOrganization: '0',
      dataType: '0',
      debugMode: '12',
      scale: 10, // 刻度
      fftSize: 1024,
      fileName: '',
      fileType: '0',
      intermediateFrequencyBandwidth: 0, // 中频带宽
      radarcenterFreqIn: 0, // 雷达中心频率
      radarintermediateFrequency: 0,
      specPoints: 8192
    },
    // 文件信息
    file: {
      id: null,
      bitRate: null,
      centerFreqIn: null, // 中心频率
      dataType: null,
      debugMode: null,
      fftSize: null,
      fileName: null,
      fileType: null,
      intermediateFrequencyBandwidth: null,
      samplingRate: null,
      uploadTime: null,
      startOffset: null,
      startOffLength: null,
      cutoffLength: null,
      signalCenterFreqIn: null,// 单体信号分析跳转频率
      signalBandwidth: null  // 单体信号分析跳转带宽
    },
    featureExtractFile: {
      id: null,
      bitRate: null,
      centerFreqIn: null, // 中心频率
      dataType: null,
      debugMode: null,
      fftSize: null,
      fileName: null,
      fileType: null,
      intermediateFrequencyBandwidth: null,
      samplingRate: null,
      uploadTime: null,
      startOffset: null,
      startOffLength: null,
      cutoffLength: null,
      signalCenterFreqIn: null,// 单体信号分析跳转频率
      signalBandwidth: null  // 单体信号分析跳转带宽
    },
    hopFrequencyFile: {
      id: null,
      bitRate: null,
      centerFreqIn: null, // 中心频率
      dataType: null,
      debugMode: null,
      fftSize: null,
      fileName: null,
      fileType: null,
      intermediateFrequencyBandwidth: null,
      samplingRate: null,
      uploadTime: null,
      startOffset: null,
      startOffLength: null,
      cutoffLength: null,
      signalCenterFreqIn: null,// 单体信号分析跳转频率
      signalBandwidth: null  // 单体信号分析跳转带宽
    },
    // 雷达文件信息
    radarfile: {
      fileName: null,
      uploadTime: null,
      radarNumber: null,
      collectionTime: null,
      fileTypeRadar: null, // 中心频率
      collectionMethod: null,
      intermediateFrequency: null,
      centerfreqIn: null,
      samplerateIn: null,
      samplingBw: null,
      samplingNum: null,
      quantizationBits: null,
      channelsNum: null,
      pulseCount: null
    },
    // 频谱分析文件信息s
    freqFiles: [],
    // 零扫宽视图
    zeroSpanViews: reactive([]),
    // 数字调制分析视图
    dmViews: reactive([]),
    sperctumArr: [],
    //下变频
    downConversion: {
      doubleMapKey: null,
      ddcFlag: false
    },
    radarDownConversion: {
      doubleMapKey: null,
      ddcFlag: false
    },
    //滤波标识
    filting: {
      doubleMapKey: null,
      filterFlag: false
    },
    radarFilting: {
      doubleMapKey: null,
      filterFlag: false
    },
    // 静态表格配置
    static: {
      enabled: true,
      dmLimit: 1024 // 静态信号解调显示个数限制
    },
    //参数估计
    paramsEstimate: {}
  }),
  getters: {
    // 总周期
    totalCycle() {
      const { IQNum, simpleRate } = this.settings
      return round((1e6 * IQNum) / simpleRate, 2)
    },
    // 一帧的点数
    viewNum() {
      const { IQNum } = this.settings
      const { swpTime } = this.global
      return round(IQNum * (swpTime / this.totalCycle))
    }
  },
  actions: {
    removeZeroView(viewName) {
      const index = this.zeroSpanViews.findIndex(item => item.name === viewName);
      if (index !== -1) {
        this.zeroSpanViews.splice(index, 1);
      }
    },
    removeDmViews(viewName) {
      const index = this.dmViews.findIndex(item => item.name === viewName);
      if (index !== -1) {
        this.dmViews.splice(index, 1);
      }
    },
    setStart(num) {
      this.start = num
    },
    setConfig(key, val) {
      this.settings[key] = val
      if (key === 'playbackRate') {
        usePlayControl().setPlaybackRate(val)
      } else if (key === 'swpTime') {
        this.setGlobal(key, val)
      }
    },
    setGlobal(key, value) {
      if (this.global[key] !== value) {
        operation.updateOptions(key, value)
        this.global[key] = parseInt(value)
      }
    },
    // 执行定时执行
    actionInterval(start) {
      this.setStart(start)
    },
    setFileInfo(info, path = '0') {
      const { uploadTime } = info
      if (uploadTime) {
        const reg = /-([0-9]+)-([0-9]+)\s([0-9]+):([0-9]+):/
        const uploadtimeStr = uploadTime.replace(reg, (...args) => {
          return args[1] + args[2] + args[3] + args[4]
        })
        console.log(info, '文件数据');

        this.file = { ...this.file, ...info }
        this.file.uploadTime = uploadtimeStr
        this.file.path = path
        const globalkeys = [
          'centerFreqIn',
          'debugMode',
          'samplingRate',
          'bitRate',
          'intermediateFrequencyBandwidth'
        ]
        globalkeys.forEach(key => {
          this.setGlobal(key, this.file[key])
        })
      }
    },
    setExtractFileInfo(info, path = '0') {
      const { uploadTime } = info
      if (uploadTime) {
        const reg = /-([0-9]+)-([0-9]+)\s([0-9]+):([0-9]+):/
        const uploadtimeStr = uploadTime.replace(reg, (...args) => {
          return args[1] + args[2] + args[3] + args[4]
        })
        this.featureExtractFile = { ...this.featureExtractFile, ...info }
        this.featureExtractFile.uploadTime = uploadtimeStr
        this.featureExtractFile.path = path
        const globalkeys = [
          'centerfreqIn',
          'debugMode',
          'samplingRate',
          'bitRate',
          'intermediateFrequencyBandwidth'
        ]
        globalkeys.forEach(key => {
          this.setGlobal(key, this.featureExtractFile[key])
        })
      }
    },
    setHopFileInfo(file) {
      this.hopFrequencyFile = JSON.parse(JSON.stringify(file));
    },


    setFreqFiles(files) {
      this.freqFiles = []
      files.forEach(item => {
        this.freqFiles.push(item)
      })
    },
    setRadarInfo(info, path = '1') {
      const { uploadTime } = info
      if (uploadTime) {
        const reg = /-([0-9]+)-([0-9]+)\s([0-9]+):([0-9]+):/
        const uploadtimeStr = uploadTime.replace(reg, (...args) => {
          return args[1] + args[2] + args[3] + args[4]
        })
        this.radarfile = { ...this.radarfile, ...info }
        this.radarfile.uploadTime = uploadtimeStr
        this.radarfile.path = path
        // const globalkeys = [
        //   'centerFreqIn', //中心频率
        //   'samplerateIn', //采样率
        //   'debugMode',
        //   'samplingRate', //samplerateIn 采样率
        //   'bitRate',
        //   'intermediateFrequency' //中频
        // ]
        // globalkeys.forEach(key => {
        //   this.setGlobal(key, this.file[key])
        // })
        this.global.radarcenterFreqIn = this.radarfile.centerfreqIn
        this.global.radarintermediateFrequency = this.radarfile.intermediateFrequency
      }
    },
    getFileInfo() {
      return this.file
    },
    getHopFileInfo() {
      return this.hopFrequencyFile
    },
    getExtractFileInfo() {
      return this.featureExtractFile
    },
    getFreqFiles() {
      return this.freqFiles
    },
    getRadarInfo() {
      return this.radarfile
    },
    setParamsEstimate(params) {
      this.paramsEstimate = params
    },
    setDownConversion(data) {
      this.downConversion.doubleMapKey = data.doubleMapKey
      this.downConversion.ddcFlag = data.ddcFlag
      this.filting.filterFlag = data.filterFlag
    },
    setRadarDownConversion(data) {
      this.radarDownConversion.doubleMapKey = data.doubleMapKey
      this.radarDownConversion.ddcFlag = data.ddcFlag
      this.radarFilting.filterFlag = data.filterFlag
    },
    getDownConversion() {
      return this.downConversion
    },
    getRadarDownConversion() {
      return this.radarDownConversion
    },
    setFilting(data) {
      this.filting.doubleMapKey = data.doubleMapKey
      this.downConversion.ddcFlag = data.ddcFlag
      this.filting.filterFlag = data.filterFlag
    },
    setRadarFilting(data) {
      this.radarFilting.doubleMapKey = data.doubleMapKey
      this.radarDownConversion.ddcFlag = data.ddcFlag
      this.radarFilting.filterFlag = data.filterFlag
    },
    setSperctum(data) {
      this.sperctumArr = data
    },
    getSperctum() {
      return this.sperctumArr
    },
    changeSperctum() {
      this.sperctumArr = []
    },
    getFilting() {
      return this.filting
    },
    getRadarFilting() {
      return this.radarFilting
    },
    getParamsEstimate() {
      return this.paramsEstimate
    },
    writeStorage() {
      console.log(this.$state, 'writeStorage——存储chart数据');
      storage.set(STORAGE_KEY, this.$state, 5000)
    },
    writeStorageFreqFiles() {
      storage.set('freqFiles', this.freqFiles, 5000)
    },
    readStorage() {
      const result = storage.get(STORAGE_KEY)
      console.log(result, '读取存储本地的chart数据');
      if (!result) {
        return
      }
      if (!this.file.fileName) {
        this.setFileInfo(result.file)
      }
      if (!this.radarfile.fileName) {
        this.setRadarInfo(result.radarfile)
      }
    },
    readFeatureExtractStorage() {
      const result = storage.get(STORAGE_KEY)
      console.log(result, '读取存储本地的chart数据');
      if (!result) {
        return
      }
      if (!this.featureExtractFile.fileName) {
        this.setExtractFileInfo(result.featureExtractFile)
      }
    },
    readHopFileStorage() {
      const result = storage.get(STORAGE_KEY)
      console.log(result, '读取存储本地的hopping数据');
      if (!result) {
        return
      }
      console.log(this.hopFrequencyFile.fileName);
      if (!this.hopFrequencyFile.fileName) {
        this.setHopFileInfo(result.hopFrequencyFile)
      }
    },

    readStorageFreqFiles() {
      const result = storage.get('freqFiles')
      if (!result?.length && this.freqFiles.length > 0) {
        return
      }
      this.setFreqFiles(result)
    },
    compareQuery(current, last) {
      let flag = false
      const keys = Object.keys(current)
      if (!last) {
        return false
      }
      for (let key of keys) {
        if (current[key] !== last[key]) {
          flag = true
          break
        }
      }
      return flag
    },
    querySequence() {
      const currentFileInfo = this.getFileInfo()
      if (this.compareQuery(currentFileInfo, this.sequential.query)) {
        this.sequential = {}
      }
      const { value, currentRequest } = this.sequential
      if (!value) {
        if (!currentRequest) {
          this.sequential.query = cloneDeep(this.getFileInfo())
          this.sequential.currentRequest = queryFileFft(this.sequential.query).then(rsp => {
            this.sequential.value = rsp
            this.setConfig('IQNum', rsp.data.pIQ_In)
            return rsp
          })
        }
        return this.sequential.currentRequest
      }
      return Promise.resolve(value)
    }
  }
})

export default useTrainingStore
