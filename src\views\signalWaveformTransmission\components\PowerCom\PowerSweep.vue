<script setup>
  const form = ref({
    powerSweep: 0,
    powerSweepStep: 0
  })

  const reset = () => {
    form.value = {
      powerSweep: 0,
      powerSweepStep: 0
    }
  }

  const setFormData = data => {
    form.value = { ...data }
  }

  defineExpose({
    form,
    reset,
    setFormData
  })
</script>

<template>
  <el-form :model="form" label-width="auto" label-position="left">
    <el-form-item label="功率扫描：">
      <el-radio-group v-model="form.powerSweep" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="manual" :value="0">手动</el-radio-button>
        <el-radio-button class="cusBtn" label="auto" :value="1">自动</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="功率扫描跨度：">
      <el-input style="width: 200px" v-model="form.powerSweepStep">
        <template #append> dB </template>
      </el-input>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
