<script setup>
  import Highcharts from 'highcharts'

  import draggable from 'highcharts/modules/draggable-points'

  // 激活 draggable-points 模块
  draggable(Highcharts)

  const form = reactive({
    standardWaveform: '正弦波',
    startPoint: 0,
    endPoint: 2047,
    startAmplitude: 0,
    endAmplitude: 0,
    // 新增参数：相位移动（点数偏移），幅度移动（加在 y 值上的偏移量）
    phaseShift: 0,
    amplitudeShift: 0
  })

  const standardWaveformOptions = [
    { label: '正弦波', value: '正弦波' },
    { label: '方波', value: '方波' },
    { label: '三角波', value: '三角波' },
    { label: '锯齿波', value: '锯齿波' },
    { label: '高斯白噪声', value: '高斯白噪声' },
    { label: '半波整流', value: '半波整流' },
    { label: '全波整流', value: '全波整流' },
    { label: '对数升', value: '对数升' },
    { label: '对数降', value: '对数降' },
    { label: '多音频', value: '多音频' },
    { label: '洛伦兹脉冲', value: '洛伦兹脉冲' },
    { label: '自定义折线', value: '自定义折线' }
  ]

  // 用来存储波形一、波形二的数据
  // xData, yData: 数组
  let waveformData1 = { xData: [], yData: [] }
  let waveformData2 = { xData: [], yData: [] }

  // 用于保存当前主图波形数据（拖拽后的数据也会更新在这里）
  let currentWaveform = { xData: [], yData: [] }

  // 根据选择的波形生成数据
  const generateWaveData = waveType => {
    const xValues = []
    const yValues = []

    const { startPoint, endPoint } = form
    let maxSignal = -Infinity
    let minSignal = Infinity

    for (let i = startPoint; i <= endPoint; i++) {
      xValues.push(i)
      switch (waveType) {
        case '正弦波':
          yValues.push(Math.sin(i * ((2 * Math.PI) / 2048))) // 正弦波
          break
        case '方波':
          yValues.push(i % 2048 < 1024 ? 1 : -1) // 方波
          break
        case '三角波':
          yValues.push(2 * Math.abs(2 * (i / 2048 - Math.floor(i / 2048 + 0.5))) - 1)
          break
        case '锯齿波':
          const T = 102.4 //周期
          const A = 2 //振幅
          const yStep = Math.floor((A * T * 100) / 2048) / 100
          console.log(Math.floor((i + T / 2) / T) * yStep - 1)
          yValues.push(Math.floor((i + T / 2) / T) * yStep - 1) // 多锯齿波
          break
        case '高斯白噪声':
          yValues.push(Math.random() * 2 - 1) // 高斯白噪声
          break
        case '半波整流':
          yValues.push(Math.max(Math.sin(i * ((2 * Math.PI) / 2048)), 0)) // 半波整流
          break
        case '全波整流':
          yValues.push(Math.abs(Math.sin(i * ((2 * Math.PI) / 2048)))) // 全波整流
          break
        case '对数升':
          yValues.push(Math.log10(i + 1) / Math.log10(2048)) // 对数升
          break
        case '对数降':
          yValues.push(-Math.log10(i + 1) / Math.log10(2048)) // 对数降
          break
        case '多音频':
          let signal = 0
          for (let n = 1; n <= 4; n++) {
            signal += Math.sin(i * ((2 * Math.PI * n) / 2048)) / 4 // 多音频
          }
          yValues.push(signal)
          // 记录多音频信号的最值
          if (signal > maxSignal) {
            maxSignal = signal
          }
          if (signal < minSignal) {
            minSignal = signal
          }
          break
        case '洛伦兹脉冲':
          {
            let x = (i - 1024) / 1024
            let sigma = 0.4
            yValues.push(
              Math.sin(i * ((2 * Math.PI) / 2048)) *
                Math.exp(-Math.pow(x, 2) / (2 * Math.pow(sigma, 2)))
            )
          }
          break
        case '自定义折线':
          // 默认是一个直线，可以根据拖动改变点的位置
          yValues.push(0)
          break
        default:
          yValues.push(0)
      }
    }

    // 对多音频信号进行归一化处理
    if (waveType === '多音频') {
      yValues.forEach((val, index) => {
        yValues[index] = -1 + (2 * (val - minSignal)) / (maxSignal - minSignal)
      })
    }
    // 保证 y 值始终在 -1 到 1 之间（如果需要）
    yValues.forEach((val, index) => {
      yValues[index] = Math.max(Math.min(val, 1), -1)
    })

    return { xValues, yValues }
  }

  // 重置功能
  const reset = () => {
    const { xValues, yValues } = generateWaveData(form.standardWaveform)
    renderChart(xValues, yValues)
  }
  // 翻转功能
  const flipFun = () => {
    const { xValues, yValues } = generateWaveData(form.standardWaveform)
    const flippedYValues = yValues.map(val => -val)
    renderChart(xValues, flippedYValues)
  }

  // 相位移动
  const applyPhaseShift = () => {
    const shift = parseInt(form.phaseShift) || 0
    if (!currentWaveform.yData.length) return

    // 直接操作 currentWaveform.yData 的拷贝
    const newY = currentWaveform.yData.slice()
    if (shift > 0) {
      // 向右移：最后 shift 个元素移到前面
      const tail = newY.splice(-shift, shift)
      newY.unshift(...tail)
    } else if (shift < 0) {
      // 向左移：前 abs(shift) 个元素移到后面
      const head = newY.splice(0, Math.abs(shift))
      newY.push(...head)
    }
    // 更新当前波形数据
    currentWaveform.yData = newY.slice()
    renderChart(currentWaveform.xData, newY)
  }

  // 幅度移动
  const applyAmplitudeShift = () => {
    const shift = parseFloat(form.amplitudeShift) || 0
    if (!currentWaveform.yData.length) return
    const newY = currentWaveform.yData.map(val => {
      return Math.max(Math.min(val + shift, 1), -1)
    })
    // 更新当前波形数据
    currentWaveform.yData = newY.slice()
    renderChart(currentWaveform.xData, newY)
  }

  // 设为波形一/二
  const setWaveform = id => {
    // 如果 currentWaveform 中已有数据（可能经过运算处理），直接使用它
    if (currentWaveform.xData.length && currentWaveform.yData.length) {
      if (id === '1') {
        waveformData1 = {
          xData: currentWaveform.xData.slice(),
          yData: currentWaveform.yData.slice()
        }
        renderChartByID('waveformChart1', currentWaveform.xData, currentWaveform.yData)
      } else if (id === '2') {
        waveformData2 = {
          xData: currentWaveform.xData.slice(),
          yData: currentWaveform.yData.slice()
        }
        renderChartByID('waveformChart2', currentWaveform.xData, currentWaveform.yData)
      }
    } else {
      // 若 currentWaveform 没有数据，则回退到使用标准波形生成数据
      const { xValues, yValues } = generateWaveData(form.standardWaveform)
      if (id === '1') {
        waveformData1 = {
          xData: xValues.slice(),
          yData: yValues.slice()
        }
        renderChartByID('waveformChart1', xValues, yValues)
      } else if (id === '2') {
        waveformData2 = {
          xData: xValues.slice(),
          yData: yValues.slice()
        }
        renderChartByID('waveformChart2', xValues, yValues)
      }
    }
  }

  // **波形相加**功能
  const waveformAddition = () => {
    // 简单地假设 xData 相同，才能逐点运算
    const yDataResult = waveformData1.yData.map((val, idx) => {
      return val + waveformData2.yData[idx]
    })
    // 绘制到主图
    renderChart(waveformData1.xData, yDataResult)
  }

  // **波形相减**功能
  const waveformSubtraction = () => {
    const yDataResult = waveformData1.yData.map((val, idx) => {
      return val - waveformData2.yData[idx]
    })
    renderChart(waveformData1.xData, yDataResult)
  }

  // **波形相乘**功能
  const waveformMultiplication = () => {
    const yDataResult = waveformData1.yData.map((val, idx) => {
      return val * waveformData2.yData[idx]
    })
    renderChart(waveformData1.xData, yDataResult)
  }

  // 产生波形并渲染到主图
  const onSubmit = () => {
    const { xValues, yValues } = generateWaveData(form.standardWaveform)
    renderChart(xValues, yValues)
  }

  // 主图渲染
  const renderChart = (xData, yData) => {
    currentWaveform = { xData: xData.slice(), yData: yData.slice() }
    Highcharts.chart('waveformChart', {
      chart: {
        type: 'line',
        backgroundColor: '#fff'
      },
      title: {
        text: ' ',
        style: {
          color: '#000'
        }
      },
      xAxis: {
        // categories: xData,
        type: 'linear',
        min: 1,
        max: 2049,
        title: {
          text: 'x-Points',
          style: { color: '#000' }
        },
        gridLineColor: '#006400',
        gridLineWidth: 1,
        tickColor: '#000',
        tickWidth: 1,
        labels: {
          style: {
            color: '#000'
          }
        },
        tickInterval: 100,
        tickPosition: 'inside'
      },
      yAxis: {
        min: -1,
        max: 1,
        title: {
          text: 'y-Amplitude',
          style: { color: '#000' }
        },
        gridLineColor: '#006400',
        gridLineWidth: 1,
        tickAmount: 11,
        labels: {
          style: {
            color: '#000'
          }
        }
      },
      reflow: true,
      legend: { enabled: false },
      credits: { enabled: false },
      accessibility: { enabled: false },
      series: [
        {
          name: form.standardWaveform,
          data: yData,
          color: '#000'
          // dragDrop: {
          //   enabled: true,
          //   draggableY: true,
          //   draggableX: true
          // },
          // point: {
          //   events: {
          //     dragStart: function (e) {
          //       // 拖拽开始时可以初始化一些值，比如记录当前点的信息
          //       console.log('拖拽开始', e)
          //     },
          //     drag: function (e) {
          //       // 拖拽中，实时更新目标点的 y 值
          //       console.log('拖拽中', e)
          //     },
          //     drop: function () {
          //       const index = this.index
          //       console.log('拖拽结束', this.index)
          //       console.log('拖拽结束', this.y)
          //       currentWaveform.yData[index] = this.y // 更新数据
          //       currentWaveform.xData[index] = this.x // 更新数据
          //       console.log('拖拽结束', currentWaveform.xData, currentWaveform.yData)
          //       renderChart(currentWaveform.xData, currentWaveform.yData)
          //     }
          //   }
          // }
        }
      ],
      plotOptions: {
        series: {
          turboThreshold: 500, // 启用 Turbo 模式，当数据量大于 1000 时启用
          boostThreshold: 1,
          marker: { enabled: false },
          animation: false,
          shadow: false, // 关闭阴影
          enableMouseTracking: true, // 如果不需要交互，禁用鼠标跟踪
          states: {
            hover: {
              enabled: false
            }
          }
        }
      },
      tooltip: {
        enabled: false
      }
    })
  }

  // 渲染到指定ID的图表（波形一/二）
  const renderChartByID = (id, xData, yData) => {
    Highcharts.chart(id, {
      chart: {
        type: 'line',
        backgroundColor: '#fff'
      },
      title: { enabled: false, text: '' },
      xAxis: {
        categories: xData,
        min: 1,
        max: 2049,
        title: {
          text: 'x-Points',
          style: { color: '#000' }
        },
        gridLineColor: '#006400',
        gridLineWidth: 1,
        tickColor: '#000',
        tickWidth: 1,
        labels: {
          style: {
            color: '#000'
          }
        },
        tickInterval: 100,
        tickPosition: 'inside'
      },
      yAxis: {
        min: -1,
        max: 1,
        title: {
          text: 'y-Amplitude',
          style: { color: '#000' }
        },
        gridLineColor: '#006400',
        gridLineWidth: 1,
        tickAmount: 11,
        labels: {
          style: {
            color: '#000'
          }
        }
      },
      legend: { enabled: false },
      credits: { enabled: false },
      accessibility: { enabled: false },
      series: [
        {
          name: form.standardWaveform,
          data: yData,
          color: '#000',
          step: true
        }
      ],
      plotOptions: {
        series: {
          turboThreshold: 500, // 启用 Turbo 模式，当数据量大于 1000 时启用
          boostThreshold: 1,
          marker: { enabled: false },
          animation: false,
          shadow: false, // 关闭阴影
          enableMouseTracking: false, // 如果不需要交互，禁用鼠标跟踪
          states: {
            hover: {
              enabled: false
            }
          }
        }
      },
      tooltip: {
        enabled: false
      }
    })
  }

  onMounted(() => {
    onSubmit()
    // 初始化波形图1、图2显示一个自定义折线
    const { xValues, yValues } = generateWaveData('自定义折线')
    renderChartByID('waveformChart1', xValues, yValues)
    renderChartByID('waveformChart2', xValues, yValues)
    waveformData1 = waveformData2 = { xData: xValues.slice(), yData: yValues.slice() }
  })
</script>

<template>
  <cu-title title="波形生成"></cu-title>
  <el-row :gutter="30">
    <el-col :span="5">
      <el-form :model="form" label-width="auto">
        <el-form-item label="标准波形：">
          <el-select style="width: 100%" v-model="form.standardWaveform" placeholder="请选择波形">
            <el-option
              v-for="item in standardWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="起始点：">
          <el-input v-model="form.startPoint" />
        </el-form-item>
        <el-form-item label="终止点：">
          <el-input v-model="form.endPoint" />
        </el-form-item>
        <el-form-item label="起始幅度：">
          <el-input :disabled="true" v-model="form.startAmplitude" />
        </el-form-item>
        <el-form-item label="终止幅度：">
          <el-input :disabled="true" v-model="form.endAmplitude" />
        </el-form-item>
      </el-form>

      <div class="text-center">
        <cu-button @click="onSubmit" content="产生波形"></cu-button>
      </div>
    </el-col>
    <el-col :span="19">
      <el-row>
        <el-col :span="20">
          <div class="text-right mb-2">
            <cu-button content="打开"></cu-button>
            <cu-button content="撤销"></cu-button>
            <cu-button content="重做" @click="reset"></cu-button>
            <cu-button content="翻转" @click="flipFun"></cu-button>
            <cu-button content="清除"></cu-button>
            <cu-button content="保存"></cu-button>
          </div>
          <div id="waveformChart" class="w-full h-[500px] bg-white"></div>
        </el-col>
        <el-col :span="4" style="display: flex; flex-direction: column; justify-content: center">
          <div class="mb-5">
            <span> 幅度移动 </span>
            <div class="flex items-end justify-between mt-2">
              <el-input
                style="width: 100px"
                v-model="form.amplitudeShift"
                placeholder="输入幅度偏移"
              />
              <cu-button content="保存" @click="applyAmplitudeShift"></cu-button>
            </div>
          </div>
          <div>
            <span> 相位移动 </span>
            <div class="flex items-end justify-between mt-2">
              <el-input style="width: 100px" v-model="form.phaseShift" placeholder="输入点数偏移">
                <template #append>
                  <span>°</span>
                </template>
              </el-input>
              <cu-button content="保存" @click="applyPhaseShift"></cu-button>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-row :gutter="20">
            <el-col :span="12" class="text-center">
              <cu-button content="设为波形一" @click="setWaveform('1')"></cu-button>
              <div id="waveformChart1" class="w-full h-[350px] bg-white"></div>
            </el-col>
            <el-col :span="12" class="text-center">
              <cu-button content="设为波形二" @click="setWaveform('2')"></cu-button>
              <div id="waveformChart2" class="w-full h-[350px] bg-white"></div>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          class="waveformAlgorithm"
          :span="4"
          style="
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          "
        >
          <cu-button content="波形相加" @click="waveformAddition"></cu-button>
          <cu-button content="波形相减" @click="waveformSubtraction"></cu-button>
          <cu-button content="波形相乘" @click="waveformMultiplication"></cu-button>
        </el-col>
      </el-row>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
  :deep(.waveformAlgorithm > .el-button + .el-button) {
    margin-left: 0 !important;
    margin-top: 20px !important;
  }
</style>
