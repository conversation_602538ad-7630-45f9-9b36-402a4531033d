<script setup>
  import { listTeacher } from '@/api/business/teacher'
  import { getClazz } from '@/api/business/clazz'
  import { listCourse, getCourseTeacher } from '@/api/business/course'
  import useDictStore from '@/store/modules/dict'

  // 定义 emits
  const emit = defineEmits(['submit'])
  const props = defineProps({
    id: {
      type: String,
      default: null
    }
  })

  const dictStore = useDictStore()
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const calzzDetailFormRef = ref(null) // 表单ref
  const teaMajorOptions = ref([]) // 专业字典数据
  const facultyOptions = ref([]) // 学院字典数据
  const teacherOptions = ref([]) // 教员字典数据
  const courseOptions = ref([]) // 课程字典数据
  const courseTeacherOptions = ref([])

  const form = ref({})
  const srCalzzCourseList = ref([]) // 课程列表
  const loading = ref(false)
  // const row = ref({
  //   courseId: null,
  //   teaId: null
  // })

  // 表单校验规则
  const teaDetailFormRules = ref({
    claCode: [{ required: true, message: '请输入期班编码', trigger: 'blur' }],
    claName: [{ required: true, message: '请输入期班名称', trigger: 'blur' }],
    claFaculty: [{ required: true, message: '请选择所属院系', trigger: 'change' }],
    claMajor: [{ required: true, message: '请选择所属专业', trigger: 'change' }],
    claMaster: [{ required: true, message: '请选择负责人', trigger: 'change' }],
    claInstructor: [{ required: true, message: '请选择指导老师', trigger: 'change' }]
  })

  const getInfo = async newId => {
    if (!newId) return
    loading.value = true
    try {
      const res = await getClazz(newId)
      form.value = res.data
    } catch (error) {
      console.error('获取教员信息失败', error)
    } finally {
      loading.value = false
    }
  }

  const handleClose = () => {
    dialogTableVisible.value = false
  }

  const handleAddSrCalzzCourse = () => {
    let obj = {}
    obj.courseId = ''
    obj.teaId = ''
    obj.calId = ''
    srCalzzCourseList.value.push(obj)
  }

  // const handleDeleteSrCalzzCourse = courseId => {
  //   srCalzzCourseList.value = srCalzzCourseList.value.filter(v => {
  //     return v.courseId != courseId
  //   })
  // }

  /**
   * 确认
   */
  const confirm = () => {
    calzzDetailFormRef.value?.validate(valid => {
      if (!valid) return
      form.srCalzzCourseList = srCalzzCourseList.value
      emit('submit', form.value)
    })
  }

  /**
   * 获取教员列表数据
   */
  const getTeacherList = async () => {
    await listTeacher({
      pageNum: 1,
      pageSize: 100
    }).then(res => {
      teacherOptions.value = res?.data?.list.map(item => ({
        label: item.teaName,
        value: item.id
      }))
    })
  }

  /**
   * 获取课程列表数据
   */
  // const getCourseList = async () => {
  //   await listCourse({
  //     pageNum: 1,
  //     pageSize: 100
  //   }).then(res => {
  //     courseOptions.value = res?.rows.map(item => ({
  //       label: item.courName,
  //       value: item.courId
  //     }))
  //   })
  // }

  /**
   *  课程选择改变
   * @param row 课程信息对象
   */
  // const courseIdChange = async row => {
  //   row.value = row
  //   await fetchCourseTeacherData(row.value.courseId)
  // }

  /**
   *  获取课程名称
   * @param courseId 课程id
   */
  // const getCourseLabel = courseId => {
  //   const option = courseOptions.value.find(item => item.value === courseId)
  //   return option ? option.label : '-'
  // }

  /**
   *  获取课程老师名称
   * @param id id 老师ID
   */
  // const getCourseLTeacherLabel = id => {
  //   const option = courseTeacherOptions.value.find(item => item.value === id)
  //   return option ? option.label : '-'
  // }

  /**
   *
   * @param courseId 课程ID
   */
  // const fetchCourseTeacherData = async courseId => {
  //   try {
  //     const res = await getCourseTeacher(courseId)
  //     courseTeacherOptions.value = res?.data.map(item => ({
  //       label: item.teaName,
  //       value: item.id
  //     }))
  //   } catch (error) {
  //     console.error('获取任课老师数据失败', error)
  //     courseTeacherOptions.value = []
  //   }
  // }

  /**
   * 获取字典数据
   */
  const getDictionaryData = async () => {
    const processDictionary = (dictType, targetOptions) => {
      dictStore.dict
        .filter(item => item.dictType === dictType)
        .forEach(item => {
          targetOptions.value.push({ label: item.dictLabel, value: item.dictValue })
        })
    }
    processDictionary('stu_major', teaMajorOptions)
    processDictionary('faculty', facultyOptions)
  }

  onMounted(async () => {
    await getDictionaryData()
    await getInfo(props.id)
    await getTeacherList()
    // await getCourseList()
  })

  defineExpose({
    teaDetailFormRules
  })
</script>

<template>
  <el-dialog
    class="px-10"
    v-model="dialogTableVisible"
    :title="id ? '编辑期班信息' : '新增期班'"
    align="center"
    width="960px"
    @closed="handleClose"
  >
    <el-form
      v-loading="loading"
      ref="calzzDetailFormRef"
      :model="form"
      :rules="teaDetailFormRules"
      label-width="auto"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="期班编码：" prop="claCode">
            <el-input v-model="form.claCode" placeholder="请输入期班编码"></el-input>
          </el-form-item>
          <el-form-item label="所属院系：" prop="claFaculty">
            <el-select v-model="form.claFaculty" placeholder="请选择所属院系">
              <el-option
                v-for="item in facultyOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="期班负责人：" prop="claMaster">
            <el-select v-model="form.claMaster" placeholder="请选择负责人">
              <el-option
                v-for="item in teacherOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="期班名称：" prop="claName">
            <el-input v-model="form.claName" placeholder="请输入期班名称"></el-input>
          </el-form-item>
          <el-form-item label="专业：" prop="claMajor">
            <el-select v-model="form.claMajor" placeholder="请选择专业">
              <el-option
                v-for="item in teaMajorOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="指导老师：" prop="claInstructor">
            <el-select v-model="form.claInstructor" placeholder="请选择指导老师">
              <el-option
                v-for="item in teacherOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 期班课程 -->
    <!-- <div class="font-bold text-left text-lg mb-3">
      <div class="mb-3">期班课程</div>
      <cu-button
        :disabled="srCalzzCourseList.length >= 20"
        content="新增"
        @click="handleAddSrCalzzCourse"
      ></cu-button>
    </div> -->
    <!-- <vxe-table
      border
      stripe
      ref="trainerTableRef"
      size="medium"
      height="200"
      :data="srCalzzCourseList"
      :keep-source="true"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        showStatus: true
      }"
      :checkbox-config="{ labelField: 'trainerList' }"
      :row-config="{ isCurrent: true, isHover: true }"
    >
      <vxe-column field="courseId" title="课程" width="200" align="center" :edit-render="{}">
        <template #default="{ row }">
          {{ getCourseLabel(row.courseId) }}
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.courseId" placeholder="请选择课程" @change="courseIdChange(row)">
            <vxe-option
              v-for="item in courseOptions"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </vxe-select>
        </template>
      </vxe-column>

      <vxe-column field="teaId" title="任课老师" width="100" align="center" :edit-render="{}">
        <template #default="{ row }">
          {{ getCourseLTeacherLabel(row.teaId) }}
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.teaId" :disabled="!row.courseId" placeholder="请选择任课老师">
            <vxe-option
              v-for="item in courseTeacherOptions"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column title="操作" width="80" align="center">
        <template #default="{ row }">
          <cu-button content="删除" @click="handleDeleteSrCalzzCourse(row.courseId)"></cu-button>
        </template>
      </vxe-column>
    </vxe-table> -->

    <template #footer>
      <div class="text-center">
        <vxe-button @click="handleClose">取消</vxe-button>
        <vxe-button status="primary" @click="confirm">确认</vxe-button>
      </div>
    </template>
  </el-dialog>
</template>

<style></style>
