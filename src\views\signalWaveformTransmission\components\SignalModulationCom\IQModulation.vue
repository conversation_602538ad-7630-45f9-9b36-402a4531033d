<script setup>
  const activeName = ref('IQModulation') //实时基带调制设置

  //IQ调制参数
  const iqModulationSettingForm = ref({
    enable: false, //启用实时基带调制
    dataOrigin: 0, //数据源
    outBwIQInput: 0, //外部宽带IQ输入
    userIQOffset: 0 //用户IQ补偿
  })

  // IQ输入调制
  const iqInputModulationForm = ref({
    enable: false, //启用
    gainBalance: 0, //增益平衡
    ioffset: 0, //I偏置
    qoffset: 0, //Q偏置
    orthogonalOffset: 0, //正交偏置
    iqCross: 0 //IQ交叉
  })

  //衰减控制
  const attenuationControlForm = ref({
    attenuation: 0, //衰减值
    modulatorAttenuation: 0 //调制器衰减
  })

  const reset = () => {
    iqModulationSettingForm.value = {
      enable: false, //启用实时基带调制
      dataOrigin: 0, //数据源
      outBwIQInput: 0, //外部宽带IQ输入
      userIQOffset: 0 //用户IQ补偿
    }
    iqInputModulationForm.value = {
      enable: false, //启用
      gainBalance: 0, //增益平衡
      ioffset: 0, //I偏置
      qoffset: 0, //Q偏置
      orthogonalOffset: 0, //正交偏置
      iqCross: 0 //IQ交叉
    }
    attenuationControlForm.value = {
      attenuation: 0, //衰减值
      modulatorAttenuation: 0 //调制器衰减
    }
  }

  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  const handleChange = newActiveName => {
    console.log(newActiveName)
    reset()
  }

  const setFormData = data => {
    iqModulationSettingForm.value = { ...data?.iqModulationSetting }
    iqInputModulationForm.value = { ...data?.iqInputModulation }
    attenuationControlForm.value = { ...data?.attenuationControl }
    if (data.iqModulationSetting?.enable) {
      activeName.value = 'IQModulation'
    } else if (data.iqInputModulation?.enable) {
      activeName.value = 'IQInputModulation'
    } else if (data.attenuationControl?.modulatorAttenuation) {
      activeName.value = 'AttenuationControl'
    }
  }

  defineExpose({
    iqModulationSettingForm,
    iqInputModulationForm,
    attenuationControlForm,
    reset,
    setFormData
  })
</script>

<template>
  <el-tabs
    v-model="activeName"
    type="border-card"
    @tab-click="handleClick"
    @tab-change="handleChange"
    tab-position="left"
  >
    <el-tab-pane label="IQ调制" name="IQModulation">
      <el-form :model="iqModulationSettingForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="iqModulationSettingForm.enable">启用</el-checkbox>
        </el-form-item>
        <el-form-item style="align-items: flex-start" label="数据源：">
          <el-radio-group
            v-model="iqModulationSettingForm.dataOrigin"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="inner" :value="0">内部</el-radio-button>
            <el-radio-button class="cusBtn" label="outside" :value="1">外部</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="外部宽带IQ输入：">
          <el-radio-group
            v-model="iqModulationSettingForm.outBwIQInput"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="on" :value="0">开</el-radio-button>
            <el-radio-button class="cusBtn" label="off" :value="1">关</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="用户IQ补偿：">
          <el-radio-group
            v-model="iqModulationSettingForm.userIQOffset"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="on" :value="0">开</el-radio-button>
            <el-radio-button class="cusBtn" label="off" :value="1">关</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="IQ输入调制" name="IQInputModulation">
      <el-form :model="iqInputModulationForm" label-width="auto" label-position="left">
        <el-form-item label="">
          <el-checkbox v-model="iqInputModulationForm.enable">启用</el-checkbox>
        </el-form-item>
        <el-form-item label="增益平衡：">
          <el-input style="width: 200px" v-model="iqInputModulationForm.gainBalance">
            <template #append> dB </template>
          </el-input>
        </el-form-item>
        <el-form-item label="I偏置：">
          <el-input style="width: 200px" v-model="iqInputModulationForm.ioffset">
            <template #append> % </template>
          </el-input>
        </el-form-item>
        <el-form-item label="Q偏置：">
          <el-input style="width: 200px" v-model="iqInputModulationForm.qoffset">
            <template #append> % </template>
          </el-input>
        </el-form-item>
        <el-form-item label="正交偏置：">
          <el-input style="width: 200px" v-model="iqInputModulationForm.orthogonalOffset">
            <template #append> deg </template>
          </el-input>
        </el-form-item>
        <el-form-item label="IQ交叉：">
          <el-radio-group v-model="iqInputModulationForm.iqCross" size="large" style="width: 200px">
            <el-radio-button class="cusBtn" label="on" :value="0">开</el-radio-button>
            <el-radio-button class="cusBtn" label="off" :value="1">关</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="衰减控制" name="AttenuationControl">
      <el-form :model="attenuationControlForm" label-width="auto" label-position="left">
        <el-form-item label="调制器衰减：">
          <el-radio-group
            v-model="attenuationControlForm.modulatorAttenuation"
            size="large"
            style="width: 200px"
          >
            <el-radio-button class="cusBtn" label="manual" :value="0">手动</el-radio-button>
            <el-radio-button class="cusBtn" label="auto" :value="1">自动</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="衰减：">
          <el-input style="width: 200px" v-model="attenuationControlForm.attenuation">
            <template #append> dB </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.cusBtnDataSource) {
    width: 20%;
  }
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
