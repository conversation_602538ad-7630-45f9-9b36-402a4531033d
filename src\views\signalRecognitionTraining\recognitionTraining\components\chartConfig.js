// chartConfig.js
import { isUndefined } from 'lodash'

export const getChartConfig = (themeStyle, scanFormStore) => ({
  chart: {
    backgroundColor: themeStyle.chartBgColor, // 图表背景色
    type: 'line', // 图表类型：折线图
    height: 450, // 图表高度
    spacingTop: 30, // 顶部间距
    spacingBottom: 20, // 底部间距
    spacingLeft: 10, // 左侧间距
    spacingRight: 20, // 右侧间距
    zoomType: 'xy', // 缩放类型：x 和 y 轴
    events: {
      redraw(ev) {
        // 检查图表中是否存在 spectrumMarkers 数组
        this.specturmMarkers &&
          // 如果存在，则对数组中的每个 marker 进行更新
          this.specturmMarkers.forEach(marker => {
            marker.update()
          })

        // 检查图表中是否存在 topMarker 标记
        this.topMarker && this.topMarker.update(themeStyle.makerColor)
      }
    }
  },
  exporting: {
    enabled: true,
    buttons: {
      contextButton: {
        menuItems: [
          'viewFullscreen',
          'separator',
          'downloadPNG',
          'downloadSVG',
          // 'downloadPDF',
          'downloadCSV',
          'downloadXLS'
        ]
      }
    },
    chartOptions: {
      chart: {
        width: 1200,  // 适合你图表的宽度
        height: 700, // 适合你图表的高度
        scale: 3  // 增加导出的清晰度
      },
      title: {
        text: 'Your Chart Title'
      }
    }
  }, // 导出功能
  reflow: true,
  credits: {
    enabled: false
  }, // 禁用版权信息
  accessibility: {
    enabled: false // 禁用无障碍功能
  },
  xAxis: {
    title: { text: '', style: { color: '#aeadad' } }, // X轴标题
    labels: {
      style: {
        color: themeStyle.labelColor
      }
    },
    events: {
      setExtremes(ev) {
        const { max, min, target } = ev
        const axisMax = ev.target.dataMax
        const axisMin = ev.target.dataMin
        if (min < axisMax / 2 && max > axisMax / 2) {
          this.chart.zoomInMid = true
        }
        const dataMin = isUndefined(min) ? axisMin : min
        const dataMax = isUndefined(max) ? axisMax : max
        const tickInterval = (dataMax - dataMin) / 10
        target.update({ max: dataMax, min: dataMin, tickInterval })
        if (isUndefined(max)) {
          if (this.chart.zoomInMid) {
            this.chart.zoomInMid = false
            setTimeout(() => {
              this.setExtremes(0, dataMax)
            })
          }
        }
      }
    },
    tickAmount: 11,
    gridLineWidth: 1,
    gridLineColor: themeStyle.gridLineColor,
    gridLineDashStyle: 'dash',
    lineColor: themeStyle.axisLineColor,
  },
  yAxis: {
    title: {
      enabled: false
    },
    min: scanFormStore.refLevel - scanFormStore.amScale * 10,
    max: scanFormStore.refLevel,
    gridLineColor: themeStyle.gridLineColor,
    gridLineDashStyle: 'dash', // 网格线样式
    gridLineWidth: 0.5, // 网格线宽度
    lineColor: themeStyle.axisLineColor, // Y轴线颜色
    tickAmount: 11, // 刻度数量
    labels: {
      style: {
        color: themeStyle.labelColor
      }
    }
  },
  title: {
    enabled: false,
    text: '',
    style: {
      color: '#f3f3f3'
    }
  },
  boost: {
    enabled: true,
    useGPUTranslations: true, // 使用 GPU 加速坐标转换
    usePreAllocated: true, // 预分配内存提高性能
    seriesThreshold: 1, // 确保 Boost 在单个系列中始终开启
    allowForce: true // 允许在某些情况下强制使用 Boost
  },
  legend: {
    enabled: false // 禁用图例
  },
  series: [
    {
      color: themeStyle.lineColor,
      marker: {
        enabled: false
      },
      animation: false,
      enableMouseTracking: false,
      // type: 'line',
      data: [],
      lineWidth: 0.5
    }
  ],
  plotOptions: {
    series: {
      turboThreshold: 500, // 启用 Turbo 模式，当数据量大于 1000 时启用
      boostThreshold: 1,
      marker: { enabled: false },
      animation: false,
      shadow: false, // 关闭阴影
      enableMouseTracking: false, // 如果不需要交互，禁用鼠标跟踪
      states: {
        hover: {
          enabled: false
        }
      }
    },
  },
})
