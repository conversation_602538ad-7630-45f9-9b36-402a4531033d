<script setup>
  import { reactive, onMounted } from 'vue'
  import CubicSpline from 'cubic-spline'

  /** ------------------ 表单 & 数据 ------------------ **/
  const form = reactive({
    standardWaveform: '正弦波',
    startPoint: 0,
    endPoint: 2047,
    startAmplitude: 0,
    endAmplitude: 0,
    phaseShift: 0,
    amplitudeShift: 0
  })

  // 标准波形类型选项
  const standardWaveformOptions = [
    { label: '正弦波', value: '正弦波' },
    { label: '方波', value: '方波' },
    { label: '三角波', value: '三角波' },
    { label: '锯齿波', value: '锯齿波' },
    { label: '高斯白噪声', value: '高斯白噪声' },
    { label: '半波整流', value: '半波整流' },
    { label: '全波整流', value: '全波整流' },
    { label: '对数升', value: '对数升' },
    { label: '对数降', value: '对数降' },
    { label: '多音频', value: '多音频' },
    { label: '洛伦兹脉冲', value: '洛伦兹脉冲' },
    { label: '自定义折线', value: '自定义折线' }
  ]

  // 两条波形数据（保存备用）
  let waveformData1 = { xData: [], yData: [] }
  let waveformData2 = { xData: [], yData: [] }
  // 主图当前波形数据
  let currentWaveform = { xData: [], yData: [] }

  /** ------------------ 拖拽相关 ------------------ **/
  let isDragging = false
  let draggingIndex = null
  let mainCanvas = null
  // 鼠标与数据点的距离阈值（单位：像素）
  const DRAG_THRESHOLD = 3

  /** ------------------ 坐标系边距 & 范围 ------------------ **/
  // 为绘制坐标轴与刻度预留边距
  const margin = { top: 20, left: 50, right: 20, bottom: 40 }
  // y 范围固定在 [-1, 1]
  const yMin = -1,
    yMax = 1
  // x 范围由表单控制
  const getXRange = () => ({
    start: Number(form.startPoint),
    end: Number(form.endPoint)
  })

  /** ------------------ 波形数据生成 ------------------ **/
  const generateWaveData = waveType => {
    const xValues = []
    const yValues = []
    let maxSignal = -Infinity,
      minSignal = Infinity
    const xStart = Number(form.startPoint)
    const xEnd = Number(form.endPoint)
    for (let i = xStart; i <= xEnd; i++) {
      xValues.push(i)
      switch (waveType) {
        case '正弦波':
          yValues.push(Math.sin(i * ((2 * Math.PI) / 2048)))
          break
        case '方波':
          yValues.push(i % 2048 < 1024 ? 1 : -1)
          break
        case '三角波':
          yValues.push(2 * Math.abs(2 * (i / 2048 - Math.floor(i / 2048 + 0.5))) - 1)
          break
        case '锯齿波': {
          const T = 102.4,
            A = 2
          const yStep = Math.floor((A * T * 100) / 2048) / 100
          yValues.push(Math.floor((i + T / 2) / T) * yStep - 1)
          break
        }
        case '高斯白噪声':
          yValues.push(Math.random() * 2 - 1)
          break
        case '半波整流':
          yValues.push(Math.max(Math.sin(i * ((2 * Math.PI) / 2048)), 0))
          break
        case '全波整流':
          yValues.push(Math.abs(Math.sin(i * ((2 * Math.PI) / 2048))))
          break
        case '对数升':
          yValues.push(Math.log10(i + 1) / Math.log10(2048))
          break
        case '对数降':
          yValues.push(-Math.log10(i + 1) / Math.log10(2048))
          break
        case '多音频': {
          let signal = 0
          for (let n = 1; n <= 4; n++) {
            signal += Math.sin(i * ((2 * Math.PI * n) / 2048)) / 4
          }
          yValues.push(signal)
          if (signal > maxSignal) maxSignal = signal
          if (signal < minSignal) minSignal = signal
          break
        }
        case '洛伦兹脉冲': {
          let x = (i - 1024) / 1024,
            sigma = 0.4
          yValues.push(
            Math.sin(i * ((2 * Math.PI) / 2048)) *
              Math.exp(-Math.pow(x, 2) / (2 * Math.pow(sigma, 2)))
          )
          break
        }
        case '自定义折线':
          yValues.push(0)
          break
        default:
          yValues.push(0)
      }
    }
    // 多音频归一化
    if (waveType === '多音频') {
      yValues.forEach((val, index) => {
        yValues[index] = -1 + (2 * (val - minSignal)) / (maxSignal - minSignal)
      })
    }
    // 限制 y 值在 [-1,1]
    yValues.forEach((val, index) => {
      yValues[index] = Math.max(Math.min(val, 1), -1)
    })
    return { xValues, yValues }
  }

  /** ------------------ 坐标转换 ------------------ **/
  /**
   *  将数据 x 映射到画布像素坐标 sx
   * @param xVal 数据 x
   * @param width 画布宽度
   * @param xStart x 范围起点
   * @param xEnd x 范围终点
   * @returns {number} 画布像素坐标 sx
   */
  const dataToScreenX = (xVal, width, xStart, xEnd) => {
    const plotWidth = width - margin.left - margin.right
    return margin.left + ((xVal - xStart) / (xEnd - xStart)) * plotWidth
  }
  /**
   * 将数据 y 映射到画布像素坐标 sy
   * @param yVal 数据 y
   * @param height
   */
  const dataToScreenY = (yVal, height) => {
    const plotHeight = height - margin.top - margin.bottom
    return margin.top + ((yMax - yVal) / (yMax - yMin)) * plotHeight
  }

  /** 将画布像素 (sx, sy) 反算为数据坐标 (xVal, yVal) 以支持 2D 拖拽 **/
  const screenToDataXY = (sx, sy, width, height) => {
    const { start: xStart, end: xEnd } = getXRange()
    const plotWidth = width - margin.left - margin.right
    const plotHeight = height - margin.top - margin.bottom
    const px = sx - margin.left
    const py = sy - margin.top
    let xVal = xStart + (px / plotWidth) * (xEnd - xStart)
    let yVal = yMax - (py / plotHeight) * (yMax - yMin)
    xVal = Math.max(Math.min(xVal, xEnd), xStart)
    yVal = Math.max(Math.min(yVal, yMax), yMin)
    return { xVal, yVal }
  }

  /** ------------------ 绘制流程 ------------------ **/
  const drawWaveform = (canvas, xData, yData) => {
    const ctx = canvas.getContext('2d')
    const width = canvas.width,
      height = canvas.height
    // 1) 填充背景（这里使用白色，可根据需要改为其他颜色）
    ctx.fillStyle = '#fff'
    ctx.fillRect(0, 0, width, height)
    // 2) 绘制网格与坐标轴
    drawGrid(ctx, width, height)
    drawAxes(ctx, width, height)
    // 3) 绘制波形（黑色折线）
    const { start: xStart, end: xEnd } = getXRange()
    ctx.strokeStyle = '#000'
    ctx.lineWidth = 2
    ctx.beginPath()
    for (let i = 0; i < xData.length; i++) {
      const sx = dataToScreenX(xData[i], width, xStart, xEnd)
      const sy = dataToScreenY(yData[i], height)
      if (i === 0) ctx.moveTo(sx, sy)
      else ctx.lineTo(sx, sy)
    }
    ctx.stroke()
  }

  /** 绘制网格：x 方向每 100 单位，y 方向每 0.2 单位 **/
  function drawGrid(ctx, width, height) {
    ctx.lineWidth = 1
    ctx.strokeStyle = '#006600'
    const { start: xStart, end: xEnd } = getXRange()
    const plotWidth = width - margin.left - margin.right
    const plotHeight = height - margin.top - margin.bottom
    for (let xVal = xStart + 100; xVal < xEnd; xVal += 100) {
      const sx = dataToScreenX(xVal, width, xStart, xEnd)
      ctx.beginPath()
      ctx.moveTo(sx, margin.top)
      ctx.lineTo(sx, margin.top + plotHeight)
      ctx.stroke()
    }
    for (let yVal = yMin + 0.2; yVal < yMax + 1e-9; yVal += 0.2) {
      const sy = dataToScreenY(yVal, height)
      ctx.beginPath()
      ctx.moveTo(margin.left, sy)
      ctx.lineTo(margin.left + plotWidth, sy)
      ctx.stroke()
    }
  }

  /** 绘制坐标轴与刻度 **/
  function drawAxes(ctx, width, height) {
    ctx.lineWidth = 2
    ctx.strokeStyle = '#000'
    ctx.fillStyle = '#000'
    ctx.font = '14px Arial'
    const plotWidth = width - margin.left - margin.right
    const plotHeight = height - margin.top - margin.bottom
    const { start: xStart, end: xEnd } = getXRange()
    ctx.beginPath()
    ctx.moveTo(margin.left, margin.top + plotHeight)
    ctx.lineTo(margin.left + plotWidth, margin.top + plotHeight)
    ctx.stroke()
    ctx.beginPath()
    ctx.moveTo(margin.left, margin.top)
    ctx.lineTo(margin.left, margin.top + plotHeight)
    ctx.stroke()
    const xStep = 400
    ctx.textAlign = 'center'
    ctx.textBaseline = 'top'
    for (let xVal = xStart; xVal <= xEnd; xVal += xStep) {
      const sx = dataToScreenX(xVal, width, xStart, xEnd)
      const sy = margin.top + plotHeight
      ctx.beginPath()
      ctx.moveTo(sx, sy)
      ctx.lineTo(sx, sy + 5)
      ctx.stroke()
      ctx.fillText(`${xVal}`, sx, sy + 8)
    }
    ctx.fillText('X-Points', margin.left + plotWidth / 2, margin.top + plotHeight + 25)
    const yStep = 0.2
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle'
    for (let yVal = yMin; yVal <= yMax + 1e-9; yVal += yStep) {
      const sy = dataToScreenY(yVal, height)
      ctx.beginPath()
      ctx.moveTo(margin.left, sy)
      ctx.lineTo(margin.left - 5, sy)
      ctx.stroke()
      ctx.fillText(yVal.toFixed(1), margin.left - 8, sy)
    }
    ctx.save()
    ctx.translate(margin.left - 30, margin.top + plotHeight / 2)
    ctx.rotate(-Math.PI / 2)
    ctx.textAlign = 'center'
    ctx.textBaseline = 'bottom'
    ctx.fillText('y-Amplitude', 0, 0)
    ctx.restore()
  }

  /** ------------------ 渲染主图/副图 ------------------ **/
  const renderChart = (xData, yData) => {
    currentWaveform = { xData: xData.slice(), yData: yData.slice() }
    if (mainCanvas) {
      drawWaveform(mainCanvas, xData, yData)
    }
  }

  const renderChartByID = (id, xData, yData) => {
    const canvas = document.getElementById(id)
    if (canvas) {
      drawWaveform(canvas, xData, yData)
    }
  }

  /** ------------------ 功能按钮回调 ------------------ **/
  const onSubmit = () => {
    const { xValues, yValues } = generateWaveData(form.standardWaveform)
    renderChart(xValues, yValues)
  }

  const reset = () => {
    const { xValues, yValues } = generateWaveData(form.standardWaveform)
    renderChart(xValues, yValues)
  }

  const flipFun = () => {
    const { xValues, yValues } = generateWaveData(currentWaveform.value)
    const flippedYValues = yValues.map(val => -val)
    renderChart(xValues, flippedYValues)
  }

  // 应用相位偏移
  const applyPhaseShift = () => {
    const shift = parseInt(form.phaseShift) || 0
    if (!currentWaveform.yData.length) return
    const newY = currentWaveform.yData.slice()
    if (shift > 0) {
      const tail = newY.splice(-shift, shift)
      newY.unshift(...tail)
    } else if (shift < 0) {
      const head = newY.splice(0, Math.abs(shift))
      newY.push(...head)
    }
    currentWaveform.yData = newY.slice()
    renderChart(currentWaveform.xData, newY)
  }

  const applyAmplitudeShift = () => {
    const shift = parseFloat(form.amplitudeShift) || 0
    if (!currentWaveform.yData.length) return
    const newY = currentWaveform.yData.map(val => Math.max(Math.min(val + shift, 1), -1))
    currentWaveform.yData = newY.slice()
    renderChart(currentWaveform.xData, newY)
  }

  const setWaveform = id => {
    if (currentWaveform.xData.length && currentWaveform.yData.length) {
      if (id === '1') {
        waveformData1 = {
          xData: currentWaveform.xData.slice(),
          yData: currentWaveform.yData.slice()
        }
        renderChartByID('waveformCanvas1', currentWaveform.xData, currentWaveform.yData)
      } else if (id === '2') {
        waveformData2 = {
          xData: currentWaveform.xData.slice(),
          yData: currentWaveform.yData.slice()
        }
        renderChartByID('waveformCanvas2', currentWaveform.xData, currentWaveform.yData)
      }
    } else {
      const { xValues, yValues } = generateWaveData(form.standardWaveform)
      if (id === '1') {
        waveformData1 = { xData: xValues.slice(), yData: yValues.slice() }
        renderChartByID('waveformCanvas1', xValues, yValues)
      } else if (id === '2') {
        waveformData2 = { xData: xValues.slice(), yData: yValues.slice() }
        renderChartByID('waveformCanvas2', xValues, yValues)
      }
    }
  }

  const waveformAddition = () => {
    const yDataResult = waveformData1.yData.map((val, idx) => val + waveformData2.yData[idx])
    renderChart(waveformData1.xData, yDataResult)
  }

  const waveformSubtraction = () => {
    const yDataResult = waveformData1.yData.map((val, idx) => val - waveformData2.yData[idx])
    renderChart(waveformData1.xData, yDataResult)
  }

  const waveformMultiplication = () => {
    const yDataResult = waveformData1.yData.map((val, idx) => val * waveformData2.yData[idx])
    renderChart(waveformData1.xData, yDataResult)
  }

  /** ------------------ 鼠标hover优化体验 ------------------   */
  function handleMouseCursor(e) {
    const rect = mainCanvas.getBoundingClientRect()
    const mouseX = e.clientX - rect.left
    const mouseY = e.clientY - rect.top
    const idx = findClosestPoint(mouseX, mouseY, rect)
    mainCanvas.style.cursor = idx >= 0 ? 'pointer' : 'default'
  }

  /** ------------------ 平滑算法 ------------------ **/
  function smoothWaveSpline(xData, yData, samplingStep = 3) {
    // 降采样（采样间隔可以调整，如每3个点取一个点）
    const sampledX = [],
      sampledY = []
    for (let i = 0; i < xData.length; i += samplingStep) {
      sampledX.push(xData[i])
      sampledY.push(yData[i])
    }
    // 确保最后一个点包含进去
    if (sampledX[sampledX.length - 1] !== xData[xData.length - 1]) {
      sampledX.push(xData[xData.length - 1])
      sampledY.push(yData[yData.length - 1])
    }

    // 使用降采样数据创建样条插值
    const spline = new CubicSpline(sampledX, sampledY)

    // 插值回原数据点
    return xData.map(x => spline.at(x))
  }

  /** ------------------ 2D 拖拽事件处理 ------------------ **/

  // 在 mousedown 时，找出离鼠标最近的点（遍历所有点）
  function findClosestPoint(mouseX, mouseY, canvasRect) {
    let minDist = Infinity
    let closestIndex = -1
    const width = canvasRect.width
    const height = canvasRect.height
    const { start: xStart, end: xEnd } = getXRange()
    for (let i = 0; i < currentWaveform.xData.length; i++) {
      const sx =
        margin.left +
        ((currentWaveform.xData[i] - xStart) / (xEnd - xStart)) *
          (width - margin.left - margin.right)
      const sy =
        margin.top +
        ((yMax - currentWaveform.yData[i]) / (yMax - yMin)) * (height - margin.top - margin.bottom)
      const dx = sx - mouseX
      const dy = sy - mouseY
      const dist = Math.sqrt(dx * dx + dy * dy)
      if (dist < minDist) {
        minDist = dist
        closestIndex = i
      }
    }
    return minDist < DRAG_THRESHOLD ? closestIndex : -1
  }

  const onMouseDown = e => {
    if (!currentWaveform.yData.length) return
    const rect = mainCanvas.getBoundingClientRect()
    const mouseX = e.clientX - rect.left
    const mouseY = e.clientY - rect.top
    const idx = findClosestPoint(mouseX, mouseY, rect)
    if (idx >= 0) {
      draggingIndex = idx
      isDragging = true
      mainCanvas.style.cursor = 'grabbing'
    }
  }

  const INFLUENCE_RADIUS = 100 //影响范围，越大越平滑

  const onMouseMove = e => {
    if (!isDragging || draggingIndex == null) {
      handleMouseCursor(e)
      return
    }
    const rect = mainCanvas.getBoundingClientRect()
    const mouseX = e.clientX - rect.left
    const mouseY = e.clientY - rect.top

    const { yVal } = screenToDataXY(mouseX, mouseY, rect.width, rect.height)

    // 计算拖拽点的y偏移量
    const deltaY = yVal - currentWaveform.yData[draggingIndex]

    // 临时数组存储拖拽过程中的数据
    const tempYData = currentWaveform.yData.slice()

    // 使用余弦函数实时更新拖拽过程（避免直接修改原数据）
    for (let i = draggingIndex - INFLUENCE_RADIUS; i <= draggingIndex + INFLUENCE_RADIUS; i++) {
      if (i >= 0 && i < tempYData.length) {
        const distance = Math.abs(i - draggingIndex)
        const influence = Math.cos(((distance / INFLUENCE_RADIUS) * Math.PI) / 2)
        tempYData[i] += deltaY * influence
        tempYData[i] = Math.max(-1, Math.min(1, tempYData[i]))
      }
    }

    renderChart(currentWaveform.xData, tempYData) // 仅用临时数据渲染实时效果
  }

  // onMouseUp中使用更好的样条平滑算法：
  const onMouseUp = () => {
    if (!isDragging) return

    const rect = mainCanvas.getBoundingClientRect()
    mainCanvas.style.cursor = 'default'
    isDragging = false

    // 拖拽结束，正式更新数据
    currentWaveform.yData = smoothWaveSpline(currentWaveform.xData, currentWaveform.yData)

    renderChart(currentWaveform.xData, currentWaveform.yData)
    draggingIndex = null
  }

  onMounted(() => {
    mainCanvas = document.getElementById('mainWaveformCanvas')
    // 获取实际显示尺寸并设置 canvas 属性尺寸（防止缩放偏差）
    const rect = mainCanvas.getBoundingClientRect()
    mainCanvas.width = rect.width
    mainCanvas.height = rect.height
    onSubmit() // 初始生成波形
    if (mainCanvas) {
      mainCanvas.addEventListener('mousedown', onMouseDown)
      mainCanvas.addEventListener('mousemove', onMouseMove)
      mainCanvas.addEventListener('mouseup', onMouseUp)
      mainCanvas.addEventListener('mouseleave', onMouseUp)
    }
    // 初始化波形图1、图2（自定义折线）
    const { xValues, yValues } = generateWaveData('自定义折线')
    renderChartByID('waveformCanvas1', xValues, yValues)
    renderChartByID('waveformCanvas2', xValues, yValues)
    waveformData1 = waveformData2 = { xData: xValues.slice(), yData: yValues.slice() }
  })
</script>

<template>
  <cu-title title="波形生成 (Canvas版，自由2D拖拽+拖拽后平滑重构)"></cu-title>
  <el-row :gutter="30">
    <!-- 左侧参数表单 -->
    <el-col :span="5">
      <el-form :model="form" label-width="auto">
        <el-form-item label="标准波形：">
          <el-select style="width: 100%" v-model="form.standardWaveform" placeholder="请选择波形">
            <el-option
              v-for="item in standardWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="起始点：">
          <el-input v-model="form.startPoint" />
        </el-form-item>
        <el-form-item label="终止点：">
          <el-input v-model="form.endPoint" />
        </el-form-item>
        <el-form-item label="起始幅度：">
          <el-input :disabled="true" v-model="form.startAmplitude" />
        </el-form-item>
        <el-form-item label="终止幅度：">
          <el-input :disabled="true" v-model="form.endAmplitude" />
        </el-form-item>
      </el-form>
      <div class="text-center">
        <cu-button @click="onSubmit" content="产生波形"></cu-button>
      </div>
    </el-col>

    <!-- 右侧主图及功能按钮 -->
    <el-col :span="19">
      <el-row>
        <el-col :span="20">
          <div class="text-right mb-2">
            <cu-button content="打开"></cu-button>
            <cu-button content="撤销"></cu-button>
            <cu-button content="重做" @click="reset"></cu-button>
            <cu-button content="翻转" @click="flipFun"></cu-button>
            <cu-button content="清除"></cu-button>
            <cu-button content="保存"></cu-button>
          </div>
          <!-- 主波形图 -->
          <canvas id="mainWaveformCanvas" class="w-full" width="800" height="400"></canvas>
        </el-col>

        <!-- 幅度移动 & 相位移动 -->
        <el-col :span="4" style="display: flex; flex-direction: column; justify-content: center">
          <div class="mb-5">
            <span> 幅度移动 </span>
            <div class="flex items-end justify-between mt-2">
              <el-input
                style="width: 100px"
                v-model="form.amplitudeShift"
                placeholder="输入幅度偏移"
              />
              <cu-button content="保存" @click="applyAmplitudeShift"></cu-button>
            </div>
          </div>
          <div>
            <span> 相位移动 </span>
            <div class="flex items-end justify-between mt-2">
              <el-input style="width: 100px" v-model="form.phaseShift" placeholder="输入点数偏移">
                <template #append>
                  <span>°</span>
                </template>
              </el-input>
              <cu-button content="保存" @click="applyPhaseShift"></cu-button>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 波形1/波形2 与波形运算 -->
      <el-row>
        <el-col :span="20">
          <el-row :gutter="20">
            <el-col :span="12" class="text-center">
              <cu-button content="设为波形一" @click="setWaveform('1')"></cu-button>
              <canvas id="waveformCanvas1" class="w-full" width="800" height="350"></canvas>
            </el-col>
            <el-col :span="12" class="text-center">
              <cu-button content="设为波形二" @click="setWaveform('2')"></cu-button>
              <canvas id="waveformCanvas2" class="w-full" width="800" height="350"></canvas>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          class="waveformAlgorithm"
          :span="4"
          style="
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          "
        >
          <cu-button content="波形相加" @click="waveformAddition"></cu-button>
          <cu-button content="波形相减" @click="waveformSubtraction"></cu-button>
          <cu-button content="波形相乘" @click="waveformMultiplication"></cu-button>
        </el-col>
      </el-row>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
  canvas {
    border: 1px solid #ccc;
    background-color: #fff; /* 白色背景，此处可根据需要调整 */
  }

  /* 让“波形运算”按钮垂直排列 */
  :deep(.waveformAlgorithm > .el-button + .el-button) {
    margin-left: 0 !important;
    margin-top: 20px !important;
  }
</style>
