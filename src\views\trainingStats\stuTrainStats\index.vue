<script setup>
  import useDictStore from '@/store/modules/dict'
  import useList from '@/api/tool/filemanage/tableFunctionPro'
  import { downloadFile } from '@/utils/fileDownload.js'
  import { getStuTRStatisticList } from '@/api/simulation/trainingRecord.js'
  import stuTrainStatsDialog from '../components/stuTrainStatsDialog.vue'

  const filterOption = ref({
    trainUser: '', //学员学号
    startTime: '', //开始时间
    endTime: '' //结束时间
  })
  const xTable = ref(null)
  const { list, loading, curPage, size, total, timeSearch } = useList(
    getStuTRStatisticList,
    null,
    filterOption,
    xTable
  )
  const rowId = ref(null)
  const stuTrainStatsDialogFlag = ref(false)

  const clearUser = () => {
    filterOption.trainUser = ''
    timeSearch(filterOption.value)
  }

  const exportRecord = async () => {
    await exportTRAnswer().then(res => {
      downloadFile(res, '训练记录')
    })
  }

  const handleDetail = row => {
    rowId.value = row.trainUser
    stuTrainStatsDialogFlag.value = true
  }

  onMounted(async () => {})
</script>
<template>
  <CuTitle title="学员训练统计" />
  <vxe-toolbar class="vxe-toolbar">
    <template #buttons>
      <vxe-input
        v-model="filterOption.trainUser"
        type="search"
        placeholder="任务名称/制定人"
        clearable
        @clear="clearUser"
      />
      <vxe-input
        v-model="filterOption.startTime"
        type="date"
        placeholder="请选择开始时间"
        class="time-button"
        clearable
      />
      <p class="mx-2"> ~ </p>
      <vxe-input
        v-model="filterOption.endTime"
        type="date"
        placeholder="请选择结束时间"
        class="time-button"
        clearable
      />
      <cu-button content="查询" />
    </template>
  </vxe-toolbar>
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    height="680"
    :data="list"
    :loading="loading"
    :keep-source="true"
    :row-config="{ isCurrent: true, isHover: true }"
  >
    <vxe-column type="seq" title="序号" width="90" fixed="left" align="center" />
    <vxe-column field="trainUser" title="学员学号" width="100" fixed="left" align="center" />
    <vxe-column field="trainNameNick" title="学员名称" width="100" fixed="left" align="center" />
    <vxe-column field="trainNum" title="训练总数" width="100" align="center" />
    <vxe-column field="trainEndNum" title="已结束训练" width="180" align="center" />
    <vxe-column field="unTrainNum" title="未训练数" width="100" align="center" />
    <vxe-column field="avgScore" title="平均得分" width="100" align="center" />
    <vxe-column field="操作" title="操作" width="180" fixed="right" align="center">
      <template #default="{ row }">
        <cu-button content="训练详情" @click="handleDetail(row)" />
      </template>
    </vxe-column>
  </vxe-table>
  <!-- 分页 -->
  <p>
    <vxe-pager
      v-model:current-page="curPage"
      v-model:page-size="size"
      class="vxe-page"
      perfect
      :total="total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total'
      ]"
    />
  </p>

  <stuTrainStatsDialog
    v-if="stuTrainStatsDialogFlag"
    v-model="stuTrainStatsDialogFlag"
    :rowId="rowId"
  />
</template>

<style lang="scss" scoped>
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }
</style>
