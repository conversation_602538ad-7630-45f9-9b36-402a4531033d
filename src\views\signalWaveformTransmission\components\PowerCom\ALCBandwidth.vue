<script setup>
  const form = ref({
    alcBandwidth: 0,
    externalCoupling: 100
  })

  const reset = () => {
    form.value = {
      alcBandwidth: 0,
      externalCoupling: 100
    }
  }

  const setFormData = data => {
    form.value = { ...data }
  }

  defineExpose({
    form,
    reset,
    setFormData
  })
</script>

<template>
  <el-form :model="form" label-width="auto" label-position="left">
    <el-form-item label="ALC带宽：">
      <el-radio-group v-model="form.alcBandwidth" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="manual" :value="0">手动</el-radio-button>
        <el-radio-button class="cusBtn" label="auto" :value="1">自动</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="带宽设置：">
      <el-radio-group
        :disabled="form.alcBandwidth === 1"
        v-model="form.externalCoupling"
        size="large"
        style="width: 400px"
      >
        <el-radio-button class="cusBWBtn" label="100" :value="100">100Hz</el-radio-button>
        <el-radio-button class="cusBWBtn" label="1000" :value="1000">1kHz</el-radio-button>
        <el-radio-button class="cusBWBtn" label="10000" :value="10000">10kHz</el-radio-button>
        <el-radio-button class="cusBWBtn" label="100000" :value="100000">100kHz</el-radio-button>
      </el-radio-group>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
    margin-bottom: 5px;
  }
  :deep(.cusBWBtn) {
    width: 25%;
    margin-bottom: 5px;
  }

  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
