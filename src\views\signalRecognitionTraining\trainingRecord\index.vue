<script setup>
  import { useRouter } from 'vue-router'
  import useDictStore from '@/store/modules/dict'
  import useList from '@/api/tool/filemanage/tableFunctionPro'
  import { downloadFile } from '@/utils/fileDownload.js'
  import { checkRole } from '@/utils/permission.js'
  import { allTRList, exportTRAnswer } from '@/api/simulation/trainingRecord.js'

  const filterOption = ref({
    taskName: '', //任务名称
    trainUser: '', //参训人
    startTime: '', //开始时间
    endTime: '' //结束时间
  })
  const fileId = ref(0)
  const xTable = ref(null)
  const router = useRouter()
  const dictStore = useDictStore()
  const taskStatusOptions = ref([]) // 任务状态 （0：未开始，1：已编辑答案，2：已发布，3：进行中，4：已完成，5：销毁）
  const trainStatusOptions = ref([]) // 训练状态（0：未训练，1：已训练，2：已评分，3：缺训）
  const { list, loading, curPage, size, total, timeSearch } = useList(
    allTRList,
    null,
    filterOption,
    xTable
  )

  // 计算操作栏的宽度
  const actionColumnWidth = computed(() => {
    // 假设每个按钮的宽度为 100px，根据实际情况调整
    const buttonWidth = 100
    const extraSpacing = 5

    if (!list.value.length) {
      return buttonWidth + extraSpacing
    }
    let maxButtonCount = 0
    list.value.forEach(row => {
      let visibleButtonCount = 0
      // 评分按钮
      if (row.hasFinished === '2' && checkRole(['teacher'])) visibleButtonCount++
      // 查看按钮总是展示
      visibleButtonCount++
      if (visibleButtonCount > maxButtonCount) {
        maxButtonCount = visibleButtonCount
      }
    })
    // 加上一些额外的间距
    return maxButtonCount * buttonWidth + extraSpacing
  })

  const clearName = () => {
    filterOption.taskName = ''
    timeSearch(filterOption.value)
  }

  const clearUser = () => {
    filterOption.trainUser = ''
    timeSearch(filterOption.value)
  }

  /**
   * 跳转至编辑/查看页面
   * @param type 查看/编辑
   */
  const handleClick = (type, row) => {
    router.push({ name: 'TrainingNotes', query: { type, data: JSON.stringify(row) } })
  }

  const exportRecord = async () => {
    await exportTRAnswer().then(res => {
      downloadFile(res, '训练记录')
    })
  }

  //  获取 任务状态 字典数据
  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'task_status')
      .forEach(item => {
        taskStatusOptions.value.push({ label: item.dictLabel, value: item.dictValue })
      })
    dictStore.dict
      .filter(item => item.dictType === 'train_status')
      .forEach(item => {
        trainStatusOptions.value.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  onMounted(async () => {
    await getDictionaryData()
  })
</script>
<template>
  <CuTitle title="训练记录" />
  <vxe-toolbar class="vxe-toolbar">
    <template #buttons>
      <vxe-input
        v-model="filterOption.taskName"
        type="search"
        placeholder="请输入任务名称"
        clearable
        @clear="clearName"
        @search="timeSearch(filterOption)"
      />
      <vxe-input
        v-model="filterOption.trainUser"
        type="search"
        placeholder="请输入参训人"
        clearable
        @clear="clearUser"
        @search="timeSearch(filterOption)"
      />
      <vxe-input
        v-model="filterOption.startTime"
        type="date"
        placeholder="请选择训练开始时间"
        class="time-button"
        clearable
      />
      <cu-button content="查询" @click="timeSearch(filterOption)" />
      <cu-button content="导出" @click="exportRecord" />
    </template>
  </vxe-toolbar>
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    height="680"
    :data="list"
    :loading="loading"
    :keep-source="true"
    :row-config="{ isCurrent: true, isHover: true }"
  >
    <vxe-column type="seq" title="序号" width="90" fixed="left" align="center" />
    <vxe-column
      field="taskName"
      title="任务名称"
      width="240"
      fixed="left"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column field="trainUser" title="参训人" width="100" align="center" />
    <vxe-column field="saveTime" title="训练时间" width="180" align="center" />
    <vxe-column
      v-hasRole="['teacher']"
      field="taskStatus"
      title="任务状态"
      width="120"
      align="center"
    >
      <template #default="{ row }">
        {{ taskStatusOptions.filter(item => item.value === row.taskStatus)[0]?.label || '-' }}
      </template>
    </vxe-column>
    <vxe-column field="hasFinished" title="训练状态" width="120" align="center">
      <template #default="{ row }">
        {{ trainStatusOptions.filter(item => item.value === row.hasFinished)[0]?.label || '-' }}
      </template>
    </vxe-column>
    <vxe-column field="opscore" title="操作得分" width="130" align="center" />
    <vxe-column field="rltscore" title="结果得分" width="130" align="center" />
    <vxe-column field="score" title="总得分" width="130" align="center" />
    <vxe-column field="grader" title="评分人" width="100" align="center" />
    <vxe-column field="graderTime" title="评分时间" width="180" align="center" />
    <vxe-column field="操作" title="操作" :width="actionColumnWidth" fixed="right" align="center">
      <template #default="{ row }">
        <cu-button content="查看" @click="handleClick('check', row)" />
        <cu-button
          v-hasRole="['teacher']"
          v-show="row.hasFinished === '2'"
          content="评分"
          @click="handleClick('edit', row)"
        />
      </template>
    </vxe-column>
  </vxe-table>
  <!-- 分页 -->
  <p>
    <vxe-pager
      v-model:current-page="curPage"
      v-model:page-size="size"
      class="vxe-page"
      perfect
      :total="total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total'
      ]"
    />
  </p>
</template>

<style lang="scss" scoped>
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }
</style>
