<script setup name="SignalAnalyse">
  import PlayBack from '@/components/PlayBack/index.vue' // 播放组件
  import useTrainingStore from '@/store/modules/simulation/training.js'
  import useTrainingFormStore from '@/store/modules/form/trainingForm.js'
  import useDictStore from '@/store/modules/dict'
  import { usePlayback } from '@/common/hooks/usePlaybackControls' // 引入封装的 usePlayback Hook
  import { v4 as uuidv4 } from 'uuid' // 用于生成唯一 ID
  import { getSignalDemodulationPlotOfAll, getSignalPlotOfAll } from '@/api/charts'
  import { getTaskTime } from '@/api/simulation/trainingMission.js'
  import { saveTaskAnswer, submitTrainAnswer } from '@/api/simulation/trainingMission.js'
  import { cloneDeep } from 'lodash'
  import { dayjs, ElMessage, ElMessageBox } from 'element-plus'
  import DataChart from './components/dataChart.vue'
  import ConstellationPlot from '@/views/analyse/modules/ConstellationPlot.vue'
  import EvmVsTime from '@/views/analyse/modules/EvmVsTime.vue'
  import Ccdf from '@/views/analyse/modules/Ccdf.vue'
  import Eye from '@/views/analyse/modules/Eye.vue'
  import IqVsTime from '@/views/analyse/modules/IqVsTime.vue'
  import UnitCom from '@/components/UnitCom/index.vue'
  import { useRouter } from 'vue-router'
  import { plotToNum } from '@/utils/utils'
  import { formatDate } from '@/utils/index.js'

  provide('chartStore', useTrainingStore())
  provide('formStore', useTrainingFormStore())

  const dictStore = useDictStore()
  const oldFileName = ref('')
  const trainingStore = useTrainingStore()
  const chartData = ref([])
  const uniqueId = uuidv4() // 生成唯一的 ID
  const router = useRouter() // 路由实例
  const route = useRoute()
  const taskId = ref(null) // 任务ID
  const form = ref({
    sigFeq: 0, // 信号频率
    sigWidth: 0, // 信号带宽
    sigAmp: 0, // 信号功率
    sigType: null, // 信号类型
    modType: null, // 解调样式
    isHop: null, // 跳频信号
    isSpr: null // 扩频信号
  })
  const chartType = ref('')
  const remainingSeconds = ref(0) // 剩余时间
  const remainingOriginSeconds = ref(0) // 原始剩余时间
  const taskRemainingTime = ref('') // 任务剩余时间
  let timer = null
  const currentChartComponent = shallowRef({
    // name: 'IQ图',
    // value: 'IqVsTime',
    // component: markRaw(IqVsTime),
    // data: null
  })
  const chartTypeList = ref([
    {
      name: 'IQ图',
      value: 'IqVsTime',
      component: markRaw(IqVsTime), // 使用 markRaw
      data: null
    },
    {
      name: '幅度vs时间',
      value: 'EvmVsTime',
      component: markRaw(EvmVsTime), // 使用 markRaw
      data: null
    },
    {
      name: 'CCDF图',
      value: 'Ccdf',
      component: markRaw(Ccdf), // 使用 markRaw
      data: null
    },
    {
      name: '星座图',
      value: 'ConstellationPlot',
      component: markRaw(ConstellationPlot), // 使用 markRaw
      data: null
    },
    {
      name: '眼图',
      value: 'Eye',
      component: markRaw(Eye), // 使用 markRaw
      data: null
    }
  ])

  const analysisViewItems = [
    { title: '时域总览', value: 'Squential' },
    { title: '幅度 vs 时间', value: 'AmVsTime' },
    { title: '幅度 vs 时间(线性)', value: 'AmLinearVsTime' },
    { title: 'FM vs 时间', value: 'PmVsTime' },
    { title: 'IQ vs 时间', value: 'IqVsTime' },
    { title: '频谱绘制', value: 'Spectrum' },
    { title: 'CCDF', value: 'Ccdf' },
    { title: '瀑布图', value: 'WaterFallPlot' }
  ]

  const digitalViewItems = [
    { title: '星座图点', value: 'ConstellationPlot' },
    { title: '频谱绘制', value: 'Spectrum' },
    { title: '眼图', value: 'Eye' },
    { title: '误差向量幅度 vs 时间', value: 'EvmVsTime' },
    { title: '幅度误差 vs 时间', value: 'AmLinearVsTime' },
    { title: '解调比特流', value: 'SymbolTable' },
    { title: '幅度调制 vs 时间', value: 'AmVsTime' },
    { title: '互补累计分析函数', value: 'Ccdf' }
  ]
  const trainOpratorts = ref([])
  const signalTypeOptions = reactive([])
  const operationStepsOptions = reactive([])
  const modulationTypeOptions = reactive([])
  const chartTypeEnums = reactive([
    {
      label: 'IqVsTime',
      value: '0'
    },
    {
      label: 'EvmVsTime',
      value: '1'
    },
    {
      label: 'Ccdf',
      value: '2'
    },
    {
      label: 'ConstellationPlot',
      value: '3'
    },
    {
      label: 'Eye',
      value: '4'
    }
  ])

  // 播放控制相关方法  // 传入 uniqueId
  const { play, clearTimer, setStartOffset, setMaxOffset, resetPlayback, playbackStore } =
    usePlayback(pollingPlayBack, uniqueId)

  const chartTypeChange = dom => {
    chartTypeEnums.map(item => {
      if (item.label === dom) {
        const opratStepStrToCheck = item.value
        const existingOperator = trainOpratorts.value.find(
          oprator => oprator.opratStepStr === opratStepStrToCheck
        )
        if (existingOperator) {
          existingOperator.opratTime = formatDate(new Date())
        } else {
          trainOpratorts.value.push({
            opratName: operationStepsOptions.find(i => i.value === item.value)?.label,
            opratTime: formatDate(new Date()),
            opratId: item.value
          })
        }
      }
    })
    // 清除当前图表的数据
    currentChartComponent.value.data = null
    // 根据新的 chartType 获取对应的组件
    const selectedChart = chartTypeList.value.find(item => item.value === chartType.value)
    if (selectedChart) {
      // 只更新 currentChartComponent.value 的属性
      currentChartComponent.value.name = selectedChart.name
      currentChartComponent.value.value = selectedChart.value
      currentChartComponent.value.component = markRaw(selectedChart.component) // 使用 markRaw
      currentChartComponent.value.data = null
      // 继续请求数据，保持原来的 startOffset
      pollingPlayBack(playbackStore.startOffset) // 使用之前的 startOffset 进行轮询请求
    }
  }

  /**
   * 轮询播放回调
   * @param startOffset 起始偏移量
   * @returns Promise<void>
   */
  async function pollingPlayBack(startOffset) {
    const body = cloneDeep(trainingStore.getFileInfo())
    oldFileName.value = body.fileName
    body.typeList = ['Spectrum', chartType.value]
    body.startOffset = startOffset || 0
    body.playBackTime = playbackStore.playBackTime
    // 判断 chartType 属于哪个分类，并调用对应函数
    if (analysisViewItems.some(item => item.value === chartType.value)) {
      await analysisRequest(body)
    } else if (digitalViewItems.some(item => item.value === chartType.value)) {
      await digitalRequest(body)
    } else {
      body.typeList = ['Spectrum']
      analysisRequest(body)
    }
  }

  const digitalRequest = async reqData => {
    let data = null
    await getSignalDemodulationPlotOfAll(reqData)
      .then(res => {
        data = res.data
      })
      .catch(err => {
        console.error('获取数据失败:', err)
        if (playbackStore.interval) {
          clearTimer() // 清除计时器和未完成的请求
          playbackStore.resetPlayback() // 重置播放状态
        }
      })

    // 如果存在计时器（即接口轮询存在），那么更改数据
    if (playbackStore.interval) {
      setStartOffset(data.startOffset)
      setMaxOffset(data.maxOffset)
    }
    chartData.value = data.Spectrum.valueList || null
    // 将接口数据分配给各个组件
    if (data[chartType.value]?.code === 200) {
      if (chartType.value === 'SymbolTable') {
        currentChartComponent.value.data = data[chartType.value].map.ints || null
      } else if (chartType.value === 'ConstellationPlot') {
        currentChartComponent.value.data = data[chartType.value].map.demoData || null
      } else if (chartType.value === 'Eye') {
        currentChartComponent.value.data = data[chartType.value].finalEyePlotFuncEntity || null
      } else {
        currentChartComponent.value.data = data[chartType.value] || null
      }
    } else {
      currentChartComponent.value.data = null
    }
  }

  const analysisRequest = async reqData => {
    let data = null
    await getSignalPlotOfAll(reqData)
      .then(res => {
        data = res.data
      })
      .catch(err => {
        console.error('获取数据失败:', err)
        if (playbackStore.interval) {
          clearTimer() // 清除计时器和未完成的请求
          playbackStore.resetPlayback() // 重置播放状态
        }
      })
    // 如果定时器存在（即接口轮询存在），那么更改数据
    if (playbackStore.interval) {
      setStartOffset(data.startOffset)
      setMaxOffset(data.maxOffset)
    }
    chartData.value = data.Spectrum.valueList || null
    // 将接口数据分配给各个组件
    if (data[chartType.value]?.code === 200) {
      if (chartType.value === 'WaterFallPlot') {
        currentChartComponent.value.data = data[chartType.value].valueList || null
      } else {
        currentChartComponent.value.data = data[chartType.value] || null
      }
    } else {
      currentChartComponent.value.data = null
      // debounce(() => ElMessage.error(data[chartType.value]?.msg), 300)()
    }
  }

  const updateValue = (key, newValue, unit) => {
    form.value[key] = plotToNum(newValue + unit) // 更新表单中的原始值
  }

  const returnBack = () => {
    clearTimer()
    router.push({ name: 'TrainingMission' })
  }

  const saveAnswer = async () => {
    const params = {
      taskId: taskId.value,
      trainOpratorts: trainOpratorts.value,
      trainResult: {
        ...form.value
      },
      trainTime: remainingOriginSeconds.value - remainingSeconds.value
    }
    await saveTaskAnswer(params).then(res => {
      if (res.code === 200) {
        ElMessage.success('保存成功')
        returnBack()
      }
    })
  }

  const stopAnswer = async () => {
    try {
      // 弹出确认框
      await ElMessageBox.confirm('是否结束本次训练？提交后即不可训练', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      // 准备保存答案的参数
      const params = {
        taskId: taskId.value,
        trainOpratorts: trainOpratorts.value,
        trainResult: {
          ...form.value
        },
        trainTime: remainingOriginSeconds.value - remainingSeconds.value
      }
      // 保存答案
      const saveResponse = await saveTaskAnswer(params)
      if (saveResponse.code !== 200) {
        throw new Error('保存答案失败')
      }
      // 提交训练答案
      const submitResponse = await submitTrainAnswer({ taskId: taskId.value })
      if (submitResponse.code !== 200) {
        throw new Error('提交训练答案失败')
      }
      // 提交成功提示并返回
      ElMessage.success('提交成功')
      returnBack()
    } catch (error) {
      // 错误处理，给出相应提示
      ElMessage.error(error.message)
    }
  }

  const formatTime = seconds => {
    const hrs = Math.floor(seconds / 3600)
    const mins = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return [hrs, mins, secs].map(num => String(num).padStart(2, '0')).join(':')
  }

  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'operation_steps')
      .forEach(item => {
        operationStepsOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
    dictStore.dict
      .filter(item => item.dictType === 'signal_type')
      .forEach(item => {
        signalTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
    dictStore.dict
      .filter(item => item.dictType === 'modulation_type')
      .forEach(item => {
        modulationTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  onBeforeMount(() => {
    // 将本地缓存写入store里面的内容
    trainingStore.readStorage()
  })

  onMounted(async () => {
    taskId.value = route.query.taskId
    await getTaskTime({ taskId: taskId.value }).then(res => {
      if (res.code === 200) {
        remainingSeconds.value = Number(res.data)
        remainingOriginSeconds.value = Number(res.data)
        taskRemainingTime.value = formatTime(res.data)
        timer = setInterval(() => {
          if (remainingSeconds.value > 0) {
            remainingSeconds.value -= 1
            taskRemainingTime.value = formatTime(remainingSeconds.value)
          } else {
            clearInterval(timer)
            saveAnswer()
          }
        }, 1000)
      }
    })
    await getDictionaryData()
    play()
  })

  onUnmounted(() => {
    if (timer) clearInterval(timer)
    clearTimer() // 清除定时器和未完成的请求
    resetPlayback() // 移除这行，避免重置播放状态
    trainingStore.writeStorage() // 持久化存储
  })

  onActivated(async () => {
    trainingStore.readStorage()
  })

  onDeactivated(async () => {
    clearTimer() // 清除定时器和未完成的请求
    trainingStore.writeStorage() // 持久化存储
  })
</script>

<template>
  <el-row :gutter="30">
    <el-col :span="16">
      <div>
        <!-- 播放控制栏 -->
        <PlayBack :pollingFunctions="pollingPlayBack" :uniqueId="uniqueId" />
        <!-- 频谱图 -->
        <data-chart id="Spectrum" :data="chartData" />
      </div>
      <div>
        <el-radio-group
          v-model="chartType"
          size="large"
          style="width: 500px; margin-bottom: 10px"
          @change="chartTypeChange"
        >
          <el-radio-button
            v-for="item in chartTypeList"
            class="cusBtn"
            :label="item.value"
            :value="item.value"
          >
            {{ item.name }}
          </el-radio-button>
        </el-radio-group>
        <component
          v-if="currentChartComponent.data"
          :is="currentChartComponent.component"
          :data="currentChartComponent.data"
        />
      </div>
    </el-col>
    <el-col :span="8">
      <div class="flex justify-between font-bold text-lg my-5">
        <div> 信号识别答案 </div>
        <div class="text-red-600"> 剩余考试时间：{{ taskRemainingTime }}</div>
      </div>
      <el-form :model="form" label-width="auto" label-position="left">
        <el-form-item label="信号频率：">
          <UnitCom
            :value="form.sigFeq"
            @update:value="(newValue, unit) => updateValue('sigFeq', newValue, unit)"
          >
          </UnitCom>
        </el-form-item>
        <el-form-item label="信号带宽：">
          <UnitCom
            :value="form.sigWidth"
            :unit-options="['kHz', 'MHz']"
            @update:value="(newValue, unit) => updateValue('sigWidth', newValue, unit)"
          >
          </UnitCom>
        </el-form-item>
        <el-form-item label="信号功率：">
          <el-input style="width: 200px" v-model="form.sigAmp">
            <template #append> dBm </template>
          </el-input>
        </el-form-item>
        <el-form-item label="信号类型：">
          <el-select style="width: 200px" v-model="form.sigType" placeholder="请选择波形">
            <el-option
              v-for="item in signalTypeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="调制样式：">
          <el-select style="width: 200px" v-model="form.modType" placeholder="请选择波形">
            <el-option
              v-for="item in modulationTypeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="跳频信号：">
          <el-radio-group v-model="form.isHop" size="large" style="width: 200px">
            <el-radio-button class="cusFormBtn" label="1" value="1">是</el-radio-button>
            <el-radio-button class="cusFormBtn" label="0" value="0">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="扩频信号：">
          <el-radio-group v-model="form.isSpr" size="large" style="width: 200px">
            <el-radio-button class="cusFormBtn" label="1" value="1">是</el-radio-button>
            <el-radio-button class="cusFormBtn" label="0" value="0">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <!-- 功能按钮 -->
      <div class="flex items-center justify-center mt-10">
        <cu-button content="退出识别" @click="returnBack"></cu-button>
        <cu-button content="保存识别结果" @click="saveAnswer"></cu-button>
        <cu-button content="结束训练" @click="stopAnswer"></cu-button>
      </div>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 20%;
  }
  :deep(.cusFormBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
