<script setup>
  import CuIQFilesDialog from '@/components/CuIQFilesDialog/index.vue'
  import StuDialog from '../stuDialog/index.vue'
  import { ElMessage } from 'element-plus'

  const emit = defineEmits(['submit'])
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const IQFileDialogFlag = ref(false) // 文件选取对话框标题
  const StuDialogFlag = ref(false) // 学员对话框标题
  const missionFormRef = ref(null) // 任务表单引用
  const trainerTableRef = ref(null) // 表格引用
  const trainerList = ref([]) // 参训人员列表
  const ids = ref([]) // 参训人员ID
  const alreadySelectedStudents = ref([]) // 已选的学生
  const form = ref({
    id: null, // 任务ID
    taskName: '', // 任务名称
    taskDescribe: '', // 任务描述
    startTime: '', // 训练起始时间
    examDuration: '', // 考试时长
    fileName: '', // 识别文件
    fileId: '' // 识别文件ID
  })

  // 表单校验规则
  const missionFormRules = ref({
    taskName: [
      { required: true, message: '任务名称不能为空', trigger: 'blur' },
      { min: 2, max: 20, message: '任务名称长度应在 2 到 20 个字符之间', trigger: 'blur' }
    ],
    taskDescribe: [
      { required: true, message: '任务描述不能为空', trigger: 'blur' },
      { min: 5, max: 100, message: '任务描述长度应在 5 到 100 个字符之间', trigger: 'blur' }
    ],
    startTime: [{ required: true, message: '请选择训练起始时间', trigger: 'change' }],
    examDuration: [
      { required: true, message: '请选择考试时长', trigger: 'change' },
      { min: 1, max: 60, message: '考试时长应在 1 到 60 分钟之间', trigger: 'change' }
    ],
    fileName: [{ required: true, message: '请选择识别文件', trigger: 'change' }]
  })

  /**
   * IQ数据修改
   * @param row 当前行数据
   */
  const IQFileEdit = async row => {
    form.value.fileName = row.fileName
    form.value.fileId = row.id
    // 手动触发 fileName 的校验
    missionFormRef.value.validateField('fileName')
  }

  const addTrainer = () => {
    StuDialogFlag.value = true
  }

  /**
   * 添加参训人员
   */
  const insertStu = async rows => {
    console.log(rows)
    alreadySelectedStudents.value = rows
    rows.forEach(row => {
      trainerList.value.push({
        ...row
      })
    })
  }

  /**
   * 删除参训人员
   */
  const deleteTrainer = async () => {
    if (ids.value.length != 0) {
      trainerList.value = trainerList.value.filter(item => !ids.value.includes(item.id))
      alreadySelectedStudents.value = alreadySelectedStudents.value.filter(
        item => !ids.value.includes(item.id)
      )
    } else {
      ElMessage.error('请选择删除的数据')
    }
  }

  const selectChangeEvent = () => {
    const selectRecords = trainerTableRef.value.getCheckboxRecords()
    ids.value = []
    selectRecords.forEach(item => {
      ids.value.push(item.id)
    })
    if (selectRecords.length != ids.value.length) {
      ids.value = []
      selectRecords.forEach(item => {
        ids.value.push(item.id)
      })
    }
  }

  const reset = () => {
    form.value = {
      id: null, // 任务ID
      taskName: '', // 任务名称
      taskDescribe: '', // 任务描述
      startTime: '', // 训练起始时间
      examDuration: '', // 考试时长
      fileName: '', // 识别文件
      fileId: '' // 识别文件ID
    }
    trainerList.value = []
  }

  const handleClose = () => {
    dialogTableVisible.value = false
    reset()
  }

  const confirm = () => {
    missionFormRef.value
      .validate()
      .then(isValid => {
        if (isValid) {
          if (trainerList.value.length === 0) {
            return ElMessage.error('请选择参训人员')
          }
          // 校验成功，执行后续操作
          dialogTableVisible.value = false
          const formData = form.value
          const trainerListData = trainerList.value
          emit('submit', { form: formData, trainerList: trainerListData })
          return Promise.resolve()
        } else {
          // 校验失败，抛出错误
          return Promise.reject('任务表单校验失败')
        }
      })
      .then(() => {
        // 这里可以添加更多校验成功后的后置操作，比如重置表单等
        missionFormRef.value.resetFields()
        reset()
      })
      .catch(error => {
        // 校验失败
      })
  }

  onMounted(() => {})
</script>

<template>
  <cu-dialog
    class="px-10"
    v-model="dialogTableVisible"
    title="任务概况"
    align="center"
    width="800"
    @confirm="confirm"
    @close="handleClose"
  >
    <!-- 任务概况 -->
    <div class="font-bold text-left text-lg mb-3">任务概况</div>
    <el-form
      ref="missionFormRef"
      :model="form"
      :rules="missionFormRules"
      label-width="auto"
      label-position="left"
    >
      <el-form-item label="任务名称：" prop="taskName">
        <el-input v-model="form.taskName" placeholder="请输入任务名称"></el-input>
      </el-form-item>
      <el-form-item label="任务描述：" prop="taskDescribe">
        <el-input
          type="textarea"
          :rows="3"
          v-model="form.taskDescribe"
          placeholder="请输入任务描述"
        ></el-input>
      </el-form-item>
      <el-form-item label="训练时间：" prop="startTime">
        <vxe-input
          v-model="form.startTime"
          type="datetime"
          placeholder="请选择训练起始时间"
          class="time-button"
          clearable
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="考试时长：" prop="examDuration">
        <vxe-input
          v-model="form.examDuration"
          type="number"
          placeholder="请填写考试时长（分钟）"
          step="1"
          clearable
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="识别文件：" prop="fileName">
        <el-input
          style="width: 82%; margin: 0 12px 0 0"
          v-model="form.fileName"
          placeholder="请输入识别文件"
          readonly
        ></el-input>
        <cu-button content="选择文件" @click="IQFileDialogFlag = true"></cu-button>
      </el-form-item>
    </el-form>
    <!-- 参训人员 -->
    <div class="font-bold text-left text-lg mb-3">
      <div class="mb-3">参训人员</div>
      <cu-button content="新增" @click="addTrainer"></cu-button>
      <cu-button content="删除" @click="deleteTrainer"></cu-button>
    </div>
    <vxe-table
      border
      stripe
      ref="trainerTableRef"
      size="medium"
      height="200"
      :data="trainerList"
      :keep-source="true"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        showStatus: true
      }"
      :checkbox-config="{ labelField: 'stuCode' }"
      :row-config="{ isCurrent: true, isHover: true }"
      @checkbox-change="selectChangeEvent"
      @checkbox-all="selectChangeEvent"
    >
      <vxe-column type="checkbox" title="学号" width="80" fixed="left" align="center" />
      <vxe-column field="stuName" title="参训人员" width="200" align="center" />
      <vxe-column field="claName" title="所属期班" width="100" align="center" />
    </vxe-table>
  </cu-dialog>

  <CuIQFilesDialog v-if="IQFileDialogFlag" v-model="IQFileDialogFlag" @upward-fun="IQFileEdit">
  </CuIQFilesDialog>

  <StuDialog
    v-if="StuDialogFlag"
    v-model="StuDialogFlag"
    :selected-students="alreadySelectedStudents"
    @upward-fun="insertStu"
  />
</template>
