<script setup>
  const form = ref({
    attenuationCoupling: 0,
    alcPw: 5,
    attenuation: 0
  })

  const reset = () => {
    form.value = {
      attenuationCoupling: 0,
      alcPw: 5,
      attenuation: 0
    }
  }

  const setFormData = data => {
    form.value = { ...data }
  }

  defineExpose({
    form,
    reset,
    setFormData
  })
</script>

<template>
  <el-form :model="form" label-width="auto" label-position="left">
    <el-form-item label="衰减耦合：">
      <el-radio-group v-model="form.attenuationCoupling" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="manual" :value="0">手动</el-radio-button>
        <el-radio-button class="cusBtn" label="auto" :value="1">自动</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="ALC功率：">
      <el-input
        :disabled="form.attenuationCoupling === 1"
        style="width: 200px"
        v-model="form.alcPw"
      >
        <template #append> dBm </template>
      </el-input>
    </el-form-item>
    <el-form-item label="衰减：">
      <el-input
        :disabled="form.attenuationCoupling === 1"
        style="width: 200px"
        v-model="form.attenuation"
      >
        <template #append> dB </template>
      </el-input>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtnDataSource) {
    width: 20%;
  }
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
