<script setup>
  import { plotToNum } from '@/utils/utils'
  import UnitCom from '@/components/UnitCom/index.vue'

  const form = ref({
    enable: false, //噪声启用
    noiseMod: 0, //噪声模式
    signalBw: 200e3, //载波信号带宽
    systemBandwidth: 200e3, //系统带宽
    noiseBwRatio: 30, //噪声/系统带宽比
    noisePwType: 0, //噪声功率计算方式
    sysBw: 30, //信噪比
    ebNo: 0.7 // Eb/No
  })

  const updateValue = (key, newValue, unit) => {
    form.value[key] = plotToNum(newValue + unit) // 更新表单中的原始值
    console.log(form.value)
  }

  const setFormData = data => {
    form.value = { ...data }
    console.log('噪声数据', form.value)
  }

  defineExpose({
    form,
    setFormData
  })
</script>

<template>
  <el-form :model="form" label-width="auto" label-position="left">
    <el-form-item label="">
      <el-checkbox v-model="form.enable">启用</el-checkbox>
    </el-form-item>
    <el-form-item label="噪声模式：">
      <el-radio-group v-model="form.noiseMod" size="large">
        <el-radio-button class="cusBtnNoise" label="additive" :value="0">
          加性噪声
        </el-radio-button>
        <el-radio-button class="cusBtnNoise" label="pure" :value="1">纯噪声</el-radio-button>
        <el-radio-button class="cusBtnNoise" label="ctWave" :value="2">
          连续波干扰
        </el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="载波信号带宽：">
      <UnitCom
        :value="form.signalBw"
        @update:value="(newValue, unit) => updateValue('signalBw', newValue, unit)"
      />
    </el-form-item>
    <el-form-item label="系统带宽：">
      <UnitCom
        :value="form.systemBandwidth"
        @update:value="(newValue, unit) => updateValue('systemBandwidth', newValue, unit)"
      />
    </el-form-item>
    <el-form-item label="噪声/系统带宽比：">
      <el-input style="width: 200px" v-model="form.noiseBwRatio">
        <template #append> % </template>
      </el-input>
    </el-form-item>
    <el-form-item label="噪声功率计算方式：">
      <el-radio-group v-model="form.noisePwType" size="large" style="width: 200px">
        <el-radio-button class="cusBtn" label="C/N" :value="0">C/N模式</el-radio-button>
        <el-radio-button class="cusBtn" label="Eb/No" :value="1">Eb/No模式</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="信噪比：" v-if="form.noisePwType === 0">
      <el-input style="width: 200px" v-model="form.sysBw">
        <template #append> dB </template>
      </el-input>
    </el-form-item>
    <el-form-item label="Eb/No：" v-else>
      <el-input style="width: 200px" v-model="form.ebNo" />
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtnNoise) {
    width: 33%;
  }
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
