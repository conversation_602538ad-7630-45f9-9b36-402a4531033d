<script setup>
  // 外部库导入
  import Highcharts from 'highcharts'
  import timeline from 'highcharts/modules/timeline'
  import exporting from 'highcharts/modules/exporting'
  import boost from 'highcharts/modules/boost'
  import exportData from 'highcharts/modules/export-data'
  import brokenAxis from 'highcharts/modules/broken-axis'
  import highchartsMore from 'highcharts/highcharts-more'
  import OfflineExporting from 'highcharts/modules/offline-exporting'

  // 自定义类导入
  import SpectrumMarker from '@/common/classes/spectrumMark'
  import TopMarker from '@/common/classes/topMarker'

  // 工具函数导入
  import { cloneDeep, round } from 'lodash'
  import { numToPlot, indexToPlot } from '@/utils/utils'
  import { getChartConfig } from './chartConfig'
  import ci from '@/common/chartInstances'
  import useThemeStyle from '@/common/hooks/themeStyle.js'

  timeline(Highcharts)
  exporting(Highcharts)
  boost(Highcharts)
  exportData(Highcharts)
  brokenAxis(Highcharts)
  highchartsMore(Highcharts)
  OfflineExporting(Highcharts)

  const props = defineProps({
    id: {
      type: String,
      default: ''
    },
    selectedMarkers: {
      type: Array,
      default: () => []
    },
    isTopFollow: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    }
  })

  const { themeStyle } = useThemeStyle() // 获取主题颜色
  const { global } = inject('chartStore') // 获取注入的全局变量（分析/IQ编辑）
  const scanFormStore = inject('formStore') // 获取注入的全局变量（分析/IQ编辑）
  console.log(global)
  console.log(scanFormStore)

  const { data } = toRefs(props)
  const instance = ref(null)
  const dataLength = ref(null)

  Highcharts.setOptions({
    chart: {
      resetZoomButton: {
        position: {
          align: 'right', // 改变按钮位置，可以选择 'left'、'right'、'center' 等
          verticalAlign: 'top', // 改变垂直对齐方式
          x: -5, // 调整 X 坐标
          y: 30 // 调整 Y 坐标
        },
        theme: {
          fill: 'transparent',
          style: {
            color: themeStyle.value.hamBergerBg // 设置按钮文本颜色
          }
        }
      }
    },
    lang: {
      viewFullscreen: '全屏显示', // 修改全屏显示按钮的文字
      downloadPNG: '下载 PNG 图片', // 修改下载 PNG 按钮的文字
      downloadSVG: '下载 SVG 图片', // 修改下载 SVG 按钮的文字
      downloadPDF: '下载 PDF', // 修改下载 PDF 按钮的文字
      downloadCSV: '下载 CSV', // 修改下载 CSV 按钮的文字
      downloadXLS: '下载 Excel', // 修改下载 Excel 按钮的文字
      resetZoom: '取消缩放' // 修改缩放按钮文字
    }
  })

  // // 计算属性，根据 centerFreq 返回频率单位
  const speUnit = computed(() => {
    if (Number(scanFormStore.centerFreqIn) < 1000) {
      return numToPlot(Number(scanFormStore.centerFreqIn), 3).slice(-2)
    }
    return numToPlot(Number(scanFormStore.centerFreqIn), 3).slice(-3)
  })

  // 计算属性，根据是否激活和选中的频标，返回需要显示的标记对象
  const markers = computed(() => {
    if (
      !instance.value ||
      Object.values(data.value).every(value => Array.isArray(value) && value.length === 0)
    ) {
      return []
    }
    const chartMarks = instance.value.specturmMarkers || []
    const target = []
    props.selectedMarkers.forEach(item => {
      let marker = chartMarks.find(mk => mk.index === item)
      if (marker) {
        marker.show()
      } else {
        marker = new SpectrumMarker(instance.value, item, document.getElementById('marker'), {
          centerFreq: Number(scanFormStore.centerFreqIn),
          bandwidth: Number(scanFormStore.intermediateFrequencyBandwidth),
          resultLen: dataLength.value
        })
      }
      target.push(marker)
    })
    props.isTopFollow && target.push(instance.value.topMarker)
    return target
  })

  /** 获取 X 轴配置 */
  const getXAisConfig = showNum => {
    const axis = cloneDeep(getChartConfig(themeStyle.value, scanFormStore).xAxis)
    const centerFreq = scanFormStore.centerFreqIn
    const bandwidth = scanFormStore.intermediateFrequencyBandwidth
    const left = centerFreq - bandwidth / 2
    axis.max = showNum - 1
    axis.min = 0
    axis.tickInterval = (showNum - 1) / 10
    axis.labels.formatter = function () {
      const val = (this.value / (showNum - 1)) * bandwidth + left
      return numToPlot(val, '', 3)
    }
    return axis
  }

  /** 获取 Y 轴配置 */
  const getYAisConfig = () => {
    const yAxis = cloneDeep(getChartConfig(themeStyle.value, scanFormStore).yAxis) // 使用动态配置
    return yAxis
  }

  // 更新图表配置
  const updateChartConfig = config => {
    try {
      instance.value.update(config, false, true)
      instance.value.redraw()
    } catch (err) {
      console.log(err)
    }
  }

  // 更新图表
  const updateChart = data => {
    if (!instance.value || !instance.value.series) {
      return
    }

    updateChartConfig({
      xAxis: getXAisConfig(data.length),
      yAxis: getYAisConfig()
    })
    dataLength.value = data.length
    updateSeries(data)

    // 手动更新 resetZoomButton 的样式
    if (instance.value.resetZoomButton) {
      instance.value.resetZoomButton.css({
        color: newColor.hamBergerBg
      })
    }
    if (instance.value.topMarker && themeStyle.value.makerColor) {
      instance.value.topMarker.update(themeStyle.value.makerColor)
    }
  }

  const initHighChart = () => {
    instance.value = new Highcharts.Chart(props.id, getChartConfig(themeStyle.value, scanFormStore))
    ci.set(instance.value, props.id)
  }

  const updateSeries = data => {
    instance.value.series?.forEach((s, i) => {
      s.setData(data, true, false)
    })
  }

  // 更新图表数据
  watch(
    () => props.data,
    val => {
      updateChart(val)
    }
  )

  // 更新峰值标记
  watch(
    () => props.isTopFollow,
    val => {
      if (!instance.value.topMarker) {
        new TopMarker(instance.value, themeStyle.value.makerColor)
      }
      instance.value.topMarker.setVisible(val)
    }
  )

  // 监听 selectedMarkers 的变化，移除取消选中的标记
  watch(
    () => props.selectedMarkers,
    (newVal, oldVal) => {
      const removedMarkers = oldVal.filter(item => !newVal.includes(item))
      const currentMarkers = instance.value.specturmMarkers || []
      removedMarkers.forEach(item => {
        const marker = currentMarkers.find(mk => mk.index === item)
        if (marker) {
          marker.remove()
        }
      })
    }
  )

  // 在图表初始化后，监听 themeStyle 变化，手动更新图表背景颜色
  watch(
    () => themeStyle.value,
    newColor => {
      if (instance.value) {
        instance.value.update(
          {
            chart: {
              backgroundColor: newColor.chartBgColor,
              resetZoomButton: {
                theme: {
                  style: {
                    color: newColor.hamBergerBg // 设置按钮文本颜色
                  }
                }
              }
            },
            xAxis: {
              lineColor: newColor.axisLineColor, // 更新 X 轴线条颜色
              gridLineColor: newColor.gridLineColor, // 更新 X 轴刻度线颜色
              labels: {
                style: {
                  color: newColor.labelColor // 更新 X 轴标签颜色
                }
              }
            },
            yAxis: {
              lineColor: newColor.axisLineColor, // 更新 X 轴线条颜色
              gridLineColor: newColor.gridLineColor, // 更新 X 轴刻度线颜色
              labels: {
                style: {
                  color: newColor.labelColor // 更新 X 轴标签颜色
                }
              }
            },
            series: [
              {
                color: newColor.lineColor // 更新图表线条颜色
              }
            ]
          },
          true
        )
      }
      // 手动更新 resetZoomButton 的样式
      if (instance.value.resetZoomButton) {
        instance.value.resetZoomButton.css({
          color: newColor.hamBergerBg
        })
      }
      if (instance.value.topMarker && themeStyle.value.makerColor) {
        instance.value.topMarker.update(themeStyle.value.makerColor)
      }
    },
    {
      deep: true
    }
  )

  onMounted(() => {
    initHighChart()
  })

  onActivated(() => {
    instance.value?.redraw()
  })

  onDeactivated(() => {})

  defineExpose({
    instance
  })
</script>

<template>
  <div class="relative">
    <!-- 图表容器 -->
    <div :id="id" />
    <div id="marker">
      <!-- 自定义SVG标记 -->
      <svg version="1.1" width="24" height="322">
        <path
          fill="#aeadad"
          d="M 0 322 L 24 322  L 12 298 Z M 12 298 L 12 0"
          stroke="#aeadad"
          stroke-width="1"
        />
        <text x="7" y="319" fill="rgba(1, 23, 13, 1)">1</text>
      </svg>
    </div>
    <!-- 显示标记信息 -->
    <div v-if="markers.length > 0" class="markers-info">
      <div>
        <span style="width: 36px">Mark</span>
        <span style="width: 75px">频率({{ speUnit }})</span>
        <span>幅度(dBm)</span>
      </div>
      <div v-for="item in markers" :key="item.index">
        <span style="width: 36px">{{ item.index }}</span>
        <span>{{
          item.point
            ? indexToPlot(
                item.point.x,
                {
                  centerFreq: Number(scanFormStore.centerFreqIn),
                  bandwidth: Number(scanFormStore.intermediateFrequencyBandwidth),
                  resultLen: dataLength
                },
                3
              )
            : '--'
        }}</span>
        <span>{{ item.point ? round(item.point.y, 3) : '--' }}</span>
      </div>
    </div>
    <!-- 显示当前活动标记的值 -->
    <!-- <div v-if="instance && instance.activeMarker" class="marker-values">
      <span v-for="item in currentValues" :key="item.label" style="white-space: nowrap">
        {{ item.label + ':' + item.value }}
      </span>
    </div> -->
  </div>
</template>

<style scoped lang="scss">
  .spectrum-container {
    position: relative;
    height: 400px;
  }

  #spectrum {
    height: 100%;
  }

  #marker {
    position: absolute;
    bottom: 48px;
    cursor: pointer;
    display: none;
    opacity: 0.6;
  }

  .markers-info {
    font-size: 12px;
    position: absolute;
    left: 50px;
    top: 35px;
    span {
      padding: 0 6px;
      display: inline-block;
    }
  }

  .marker-values {
    font-size: 12px;
    position: absolute;
    left: 60px;
    top: 12px;
    width: 300px;

    span {
      padding: 0 6px;
      display: inline-block;

      &:nth-child(4),
      &:nth-child(1) {
        width: 106px;
      }
    }
  }

  .highcharts-reset-zoom {
    top: 20px !important; /* 向下调整 reset zoom 按钮 */
    right: 20px !important; /* 向右调整 reset zoom 按钮 */
  }

  .highcharts-context-button {
    top: 10px !important; /* 向下调整导出按钮 */
    right: 10px !important; /* 向右调整导出按钮 */
  }
</style>
