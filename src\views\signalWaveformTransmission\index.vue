<script setup>
  import SignalModulationCom from './components/SignalModulationCom/index.vue'
  import FreqCom from './components/FreqCom.vue'
  import NoiseCom from './components/NoiseCom.vue'
  import DeclineCom from './components/DeclineCom.vue'
  import PowerCom from './components/PowerCom/index.vue'
  import DeviceTest from '../system/equipment/components/DeviceTest.vue'
  import { getSimulationTaskDetail } from '@/api/simulation/signalSimulation.js'
  import { useRoute, useRouter } from 'vue-router'

  let timer = null
  const route = useRoute()
  const activeName = ref('SignalModulationCom')
  const taskId = ref('')
  const SignalModulationComRef = ref(null)
  const signalModulationComForm = ref({})
  const FreqComRef = ref(null)
  const NoiseComRef = ref(null)
  const PowerComRef = ref(null)
  const DeclineRef = ref(null)
  const deviceTestFlag = ref(false) // 设备测试标志
  const loading = ref(false) // 加载标志
  const deviceTestInfo = ref([
    {
      deviceAddress: '设备地址：*************',
      devicePort: '设备端口：7777',
      workStatus: '工作状态：未连接',
      faultDescription: ''
    }
  ])

  // 计算属性：判断是否有记录包含 faultDescription（非空字符串）
  const hasFaultDescription = computed(() => {
    return deviceTestInfo.value.some(
      item => item.faultDescription && item.faultDescription.trim() !== ''
    )
  })

  // 根据是否存在 faultDescription 决定表格的背景色
  const tableBgClass = computed(() => {
    return hasFaultDescription.value ? 'row-fault' : 'row-noFault'
  })

  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  const saveTemplate = () => {
    const SignalModulationComForm = {
      ...SignalModulationComRef.value.SignalSettingsRef.form,
      ...SignalModulationComRef.value.AnalogModulationRef.form
    }
    const FreqComForm = FreqComRef.value.form
    const NoiseComForm = NoiseComRef.value.form
    const PowerComForm = {
      ...PowerComRef.value.PowerSettingsRef.form,
      ...PowerComRef.value.AttenuationControlRef.form,
      ...PowerComRef.value.LoopControlRef.form,
      ...PowerComRef.value.AmplitudeStabMethodRef.form,
      ...PowerComRef.value.ALCBandwidthRef.form,
      ...PowerComRef.value.PowerSweepRef.form
    }
    const DeclineForm = DeclineRef.value.form
    console.log(SignalModulationComForm, FreqComForm, NoiseComForm, PowerComForm, DeclineForm)
  }

  const preview = () => {
    console.log('preview')
  }

  const signalEmission = () => {
    const realTimeBaseBandForm = SignalModulationComRef.value.SignalSettingsRef.realTimeBaseBandForm //实时基带设置
    const dualToneForm = SignalModulationComRef.value.SignalSettingsRef.dualToneForm //双音信号设置
    const multiToneForm = SignalModulationComRef.value.SignalSettingsRef.multiToneForm //多音信号设置
    const amplitudeModulationForm =
      SignalModulationComRef.value.AnalogModulationRef.amplitudeModulationForm //幅度调制设置
    const freqModulationForm = SignalModulationComRef.value.AnalogModulationRef.freqModulationForm //频率调制设置
    const phaseModulationForm = SignalModulationComRef.value.AnalogModulationRef.phaseModulationForm //相位调制设置
    const iqModulationSettingForm =
      SignalModulationComRef.value.IQModulationRef.iqModulationSettingForm //IQ调制参数
    const iqInputModulationForm = SignalModulationComRef.value.IQModulationRef.iqInputModulationForm //IQ调制输入信号设置
    const attenuationControlForm =
      SignalModulationComRef.value.IQModulationRef.attenuationControlForm //衰减控制
    const FreqComForm = FreqComRef.value.form //频率设置
    const NoiseComForm = NoiseComRef.value.form //噪声设置
    const DeclineForm = DeclineRef.value.form //衰减设置
    const alcBandwidthForm = PowerComRef.value.ALCBandwidthRef.form //ALC带宽设置
    const powerSweepForm = PowerComRef.value.PowerSweepRef.form //功率扫描设置
    const pwSettingForm = PowerComRef.value.PowerSettingsRef.form //功率设置
    const attenuationControlPwForm = PowerComRef.value.AttenuationControlRef.form //功率衰减控制设置
    const loopControlForm = PowerComRef.value.LoopControlRef.form //环路控制设置
    const amplitudeStabilizationForm = PowerComRef.value.AmplitudeStabMethodRef.form //功率幅度稳定方法设置
    const formData = {
      signalSimulation: {
        freq: FreqComForm,
        noise: NoiseComForm,
        decline: DeclineForm,
        power: {
          alcBandwidth: alcBandwidthForm,
          powerSweep: powerSweepForm,
          pwSetting: pwSettingForm,
          attenuationControlPw: attenuationControlPwForm,
          loopControl: loopControlForm,
          amplitudeStabilization: amplitudeStabilizationForm
        },
        sigDemod: {
          analogModulation: {
            amplitudeModulation: amplitudeModulationForm,
            freqModulation: freqModulationForm,
            phaseModulation: phaseModulationForm
          },
          iqModulation: {
            attenuationControl: attenuationControlForm,
            iqInputModulation: iqInputModulationForm,
            iqModulationSetting: iqModulationSettingForm
          },
          setting: {
            realTimeBaseBand: realTimeBaseBandForm,
            dualTone: dualToneForm,
            multiTone: multiToneForm
          }
        }
      }
    }
    console.log(formData)
  }

  const equipTest = () => {
    deviceTestFlag.value = true
  }

  const getTaskById = async () => {
    loading.value = true
    await getSimulationTaskDetail({ id: taskId.value })
      .then(res => {
        if (res.code === 200) {
          setFormData(res.data)
          signalModulationComForm.value = res.data.signalSimulation.sigDemod
        } else {
          ElMessage.error(res.msg)
        }
      })
      .finally(() => {
        timer = setTimeout(() => {
          loading.value = false
        }, 200)
      })
  }

  const setFormData = data => {
    nextTick(() => {
      SignalModulationComRef.value.SignalSettingsRef.setFormData(
        data.signalSimulation.sigDemod?.setting
      )
      SignalModulationComRef.value.AnalogModulationRef.setFormData(
        data.signalSimulation.sigDemod?.analogModulation
      )
      // SignalModulationComRef.value.IQModulationRef.setFormData(
      //   data.signalSimulation.sigDemod?.iqModulation
      // )
      FreqComRef.value.setFormData(data.signalSimulation?.freq)
      NoiseComRef.value.setFormData(data.signalSimulation?.noise)
      // DeclineRef.value.setFormData(data.signalSimulation.decline)
      // PowerComRef.value.PowerSettingsRef.setFormData(data.signalSimulation.power.pwSetting)
      // PowerComRef.value.AttenuationControlRef.setFormData(
      //   data.signalSimulation.power.attenuationControlPw
      // )
      // PowerComRef.value.LoopControlRef.setFormData(data.signalSimulation.power.loopControl)
      // PowerComRef.value.AmplitudeStabMethodRef.setFormData(
      //   data.signalSimulation.power.amplitudeStabilization
      // )
      // PowerComRef.value.ALCBandwidthRef.setFormData(data.signalSimulation.power.alcBandwidth)
      // PowerComRef.value.PowerSweepRef.setFormData(data.signalSimulation.power.powerSweep)
    })
  }

  onMounted(() => {
    if (route.query?.id) {
      taskId.value = route.query.id
      getTaskById()
    }
  })

  onBeforeUnmount(() => {
    clearTimeout(timer)
  })

  // 监听路由 query 变化（如果组件复用时切换路由，确保能响应变化）
  watch(
    () => route.query.id,
    (newId, oldId) => {
      if (newId && newId !== oldId) {
        taskId.value = newId
        getTaskById()
      }
    }
  )
</script>

<template>
  <div class="relative h-full">
    <cu-title title="电磁信号识别模拟软件"></cu-title>
    <div class="flex gap-8">
      <el-tabs
        class="w-5/6"
        v-model="activeName"
        type="border-card"
        @tab-click="handleClick"
        tab-position="left"
      >
        <el-tab-pane label="信号与解调" name="SignalModulationCom">
          <SignalModulationCom ref="SignalModulationComRef" :form-data="signalModulationComForm" />
        </el-tab-pane>
        <el-tab-pane label="频率" name="FreqCom"><FreqCom ref="FreqComRef" /></el-tab-pane>
        <el-tab-pane label="功率" name="PowerCom"><PowerCom ref="PowerComRef" /></el-tab-pane>
        <el-tab-pane label="噪声" name="NoiseCom"><NoiseCom ref="NoiseComRef" /></el-tab-pane>
        <el-tab-pane label="衰落" name="Decline"><DeclineCom ref="DeclineRef" /></el-tab-pane>
      </el-tabs>
      <div class="funBtn flex flex-col justify-center gap-10">
        <!-- <cu-button @click="saveTemplate" content="保存为模板"></cu-button> -->
        <cu-button @click="preview" content="预览"></cu-button>
        <cu-button @click="signalEmission" content="信号发射"></cu-button>
        <cu-button @click="equipTest" content="设备状态检测"></cu-button>
      </div>
    </div>
    <div class="absolute -bottom-4 -right-4">
      <vxe-table
        class="max-w-[1000px] deviceTable"
        :class="tableBgClass"
        :show-header="false"
        :data="deviceTestInfo"
        border
      >
        <vxe-column field="deviceAddress" title="设备地址" width="160" align="center" />
        <vxe-column field="devicePort" title="设备端口" width="100" align="center" />
        <vxe-column field="workStatus" title="工作状态" width="100" align="center" />
        <!-- 如果存在 faultDescription，则显示该列 -->
        <vxe-column
          v-if="hasFaultDescription"
          field="faultDescription"
          title="故障描述"
          align="center"
        />
      </vxe-table>
    </div>
  </div>

  <DeviceTest v-model="deviceTestFlag" />
</template>

<style scoped lang="scss">
  :deep(.deviceTable .vxe-table--body) {
    color: #fff !important;
  }
  :deep(.row-noFault .vxe-table--body) {
    background-color: #56bcbe !important ;
  }
  :deep(.row-fault .vxe-table--body) {
    background-color: #e89e42 !important ;
  }
  :deep(.funBtn .vxe-button + .vxe-button) {
    margin-left: 0 !important;
  }
</style>
