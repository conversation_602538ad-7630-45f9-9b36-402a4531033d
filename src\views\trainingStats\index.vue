<script setup>
  import * as echarts from 'echarts'
  import useDictStore from '@/store/modules/dict'
  import dayjs from 'dayjs'
  import { getTRStatistic } from '@/api/simulation/trainingRecord.js'

  const dictStore = useDictStore()
  const signalTypeOptions = reactive([])
  const pieChartRef = ref(null)
  const barChartRef = ref(null)

  const filterOption = ref({
    startTime: '', // 开始时间
    endTime: '' // 结束时间
  })

  // 模拟数据，后续会根据接口数据更新
  const pieData = ref([])
  const averageData = ref([])
  const maxData = ref([])

  const getStatistic = async () => {
    const params = {
      startTime: filterOption.value.startTime
        ? dayjs(filterOption.value.startTime, 'YYYY-MM-DD').format('YYYY-MM-DD')
        : '',
      endTime: filterOption.value.endTime
        ? dayjs(filterOption.value.endTime, 'YYYY-MM-DD').format('YYYY-MM-DD')
        : ''
    }
    await getTRStatistic(params).then(res => {
      // 清空之前的数据
      pieData.value = []
      averageData.value = []
      maxData.value = []
      const trainCountData = []

      // 新增空数据判断
      const isEmptyData = !res.data || res.data.length === 0

      if (isEmptyData) {
        // 折线图无数据配置
        const emptyLineOption = {
          title: [
            {
              text: '各信号类型参训人数折线图',
              left: 'center',
              top: 10
            },
            {
              text: '暂无数据',
              left: 'center',
              top: '55%',
              textStyle: {
                fontSize: 16,
                color: '#999'
              }
            }
          ],
          xAxis: { show: false },
          yAxis: { show: false },
          series: []
        }
        echarts.init(pieChartRef.value).setOption(emptyLineOption)

        // 柱状图无数据配置
        const emptyBarOption = {
          title: [
            {
              text: '得分统计柱状图',
              left: 'center',
              top: 10
            },
            {
              text: '暂无数据',
              left: 'center',
              top: '55%',
              textStyle: {
                fontSize: 16,
                color: '#999'
              }
            }
          ],
          xAxis: { show: false },
          yAxis: { show: false },
          series: []
        }
        echarts.init(barChartRef.value).setOption(emptyBarOption)
        return
      }

      // 遍历 signalTypeOptions 进行数据匹配

      signalTypeOptions.forEach(option => {
        const dataItem = res.data.find(item => item.sigType === option.value)
        if (dataItem) {
          const trainCount = Number(dataItem.trainCount)
          trainCountData.push(trainCount)
          averageData.value.push(dataItem.avgScore)
          maxData.value.push(dataItem.maxScore)
        } else {
          // 如果没有匹配到数据，设置为 0

          trainCountData.push(0)
          averageData.value.push(0)
          maxData.value.push(0)
        }
      })

      // 更新参训人数折线图
      const trainCountChart = echarts.init(pieChartRef.value)
      const trainCountOption = {
        title: {
          text: '各信号类型参训人数折线图',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: signalTypeOptions.map(item => item.label),
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '参训人数',
            type: 'line',
            data: trainCountData
          }
        ]
      }
      trainCountChart.setOption(trainCountOption)

      // 更新得分统计柱状图
      const barChart = echarts.init(barChartRef.value)
      const barOption = {
        title: {
          text: '得分统计柱状图',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['平均值', '最高分'],
          top: '5%',
          right: '5%'
        },
        xAxis: {
          type: 'category',
          data: signalTypeOptions.map(item => item.label),
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '平均值',
            type: 'bar',
            data: averageData.value
          },
          {
            name: '最高分',
            type: 'bar',
            data: maxData.value
          }
        ]
      }
      barChart.setOption(barOption)
    })
  }

  onBeforeMount(() => {
    dictStore.dict
      .filter(item => item.dictType === 'signal_type')
      .forEach(item => {
        signalTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  })

  onMounted(async () => {
    await getStatistic()
  })
</script>

<template>
  <div>
    <div class="flex items-center">
      <vxe-input
        v-model="filterOption.startTime"
        type="date"
        placeholder="请选择训练起始时间"
        clearable
        style="width: 300px"
      />
      <p class="mx-2"> ~ </p>
      <vxe-input
        v-model="filterOption.endTime"
        clearable
        type="date"
        placeholder="请选择训练截止时间"
        style="width: 300px"
      />
      <cu-button class="ml-5" content="统计" @click="getStatistic"></cu-button>
    </div>
    <div ref="pieChartRef" class="h-[300px]"></div>
    <div ref="barChartRef" class="h-[300px]"></div>
  </div>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
