<script setup>
  import { plotToNum } from '@/utils/utils'
  import UnitCom from '@/components/UnitCom/index.vue'
  import useDictStore from '@/store/modules/dict'
  import { modulationTypeOptions } from '@/constant/simulation.js'

  const dictStore = useDictStore()
  const activeName = ref('MP') //默认展示调制参数
  const filterTypeOptions = reactive([])
  const dataSourceOptions = reactive([])

  // 调制参数
  const modulationParaForm = ref({
    sampleRate: 100e6,
    modDeep: 'Nature',
    modulationType: 'QPSK',
    alpha: 30,
    equivalanceFactor: 0.5,
    cpmIndex: 0.5,
    cpmOrder: 2,
    fskDeviation: 100e3,
    askIndex: 0.1
  })

  // 数据源
  const dataSourceForm = ref({
    dataSourceType: 'PN12'
  })

  // 滤波器
  const filterForm = ref({
    filterType: 'RootRaisedCosine', //标准波形
    phaseModOffset: 0.22 //频率
  })

  const reset = () => {
    modulationParaForm.value = {
      modDeep: 'Nature',
      modulationType: 'QPSK',
      alpha: 30,
      equivalanceFactor: 0.5,
      cpmIndex: 0.5,
      cpmOrder: 2,
      fskDeviation: 100e3,
      askIndex: 0.1
    }
    dataSourceForm.value = {
      dataSourceType: 'PN12'
    }
    filterForm.value = {
      filterType: 'RootRaisedCosine', //标准波形
      phaseModOffset: 0.22 //频率
    }
  }

  const updateValue = (key, form, newValue, unit) => {
    form[key] = plotToNum(newValue + unit) // 更新表单中的原始值
  }
  const handleClick = (tab, event) => {
    console.log(tab, event)
  }

  const handleChange = newActiveName => {
    console.log(newActiveName)
  }

  const setFormData = data => {
    modulationParaForm.value = { ...data.amplitudeModulation }
    dataSourceForm.value = { ...data.freqModulation }
    filterForm.value = { ...data.phaseModulation }
    if (data.amplitudeModulation?.enable) {
      activeName.value = 'AM'
    } else if (data.freqModulation?.enable) {
      activeName.value = 'FM'
    } else if (data.phaseModulation?.enable) {
      activeName.value = 'PM'
    } else {
      activeName.value = 'AM'
    }
  }

  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'digital_mod_filter')
      .forEach(item => {
        filterTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })

    dictStore.dict
      .filter(item => item.dictType === 'digital_mod_data_source')
      .forEach(item => {
        dataSourceOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  onMounted(() => {
    getDictionaryData()
  })

  defineExpose({
    modulationParaForm,
    dataSourceForm,
    filterForm,
    reset,
    setFormData
  })

  const handleChangeModulationType = val => {
    console.log(val)
  }
</script>

<template>
  <el-tabs
    v-model="activeName"
    type="border-card"
    @tab-click="handleClick"
    @tab-change="handleChange"
    tab-position="top"
  >
    <el-tab-pane label="调制参数" name="MP">
      <el-form :model="modulationParaForm" label-width="160" label-position="left">
        <el-form-item label="采样率：">
          <UnitCom
            :value="modulationParaForm.sampleRate"
            @update:value="
              (newValue, unit) => updateValue('sampleRate', modulationParaForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item label="调制深度：">
          <el-radio-group v-model="modulationParaForm.modDeep" size="large" style="width: 200px">
            <el-radio-button class="cusBtn" label="Nature" value="Nature">Nature</el-radio-button>
            <el-radio-button class="cusBtn" label="Gray" value="Gray">Gray</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调制样式：">
          <el-cascader
            v-model="modulationParaForm.modulationType"
            :options="modulationTypeOptions"
            :show-all-levels="false"
            @change="handleChangeModulationType"
          />
        </el-form-item>
        <el-form-item v-if="modulationParaForm.modulationType[1] === 'AQPSK'" label="Alpha(deg)">
          <el-input-number
            style="width: 200px"
            v-model="modulationParaForm.alpha"
            :min="17.55"
            :max="72.45"
            :precision="2"
            :step="0.01"
          >
          </el-input-number>
        </el-form-item>
        <el-form-item
          v-if="modulationParaForm.modulationType[1] === 'UQPSK'"
          label="Equivalance Factor:"
        >
          <el-input-number
            style="width: 200px"
            v-model="modulationParaForm.equivalanceFactor"
            :min="0"
            :max="1"
            :precision="2"
            :step="0.01"
          >
          </el-input-number>
        </el-form-item>
        <el-form-item
          v-if="modulationParaForm.modulationType[1] === 'CPM'"
          label="CPM Modulation index:"
        >
          <el-input-number
            style="width: 200px"
            v-model="modulationParaForm.cpmIndex"
            :min="0"
            :max="1"
            :precision="2"
            :step="0.01"
          >
          </el-input-number>
        </el-form-item>
        <el-form-item
          v-if="modulationParaForm.modulationType[1] === 'CPM'"
          label="CPM Modulation order:"
        >
          <el-input-number
            style="width: 200px"
            v-model="modulationParaForm.cpmOrder"
            :min="1"
            :max="20"
            :step="1"
          >
          </el-input-number>
        </el-form-item>
        <el-form-item v-if="modulationParaForm.modulationType[0] === 'FSK'" label="FSK Deviation:">
          <UnitCom
            :value="modulationParaForm.fskDeviation"
            :unit-options="['kHz', 'MHz']"
            :min="1000"
            :max="80000000"
            @update:value="
              (newValue, unit) => updateValue('fskDeviation', modulationParaForm, newValue, unit)
            "
          />
        </el-form-item>
        <el-form-item
          v-if="modulationParaForm.modulationType[1] === 'ASK'"
          label="Modulation index:"
        >
          <el-input-number
            style="width: 200px"
            v-model="modulationParaForm.askIndex"
            :min="0"
            :max="1"
            :step="0.01"
          >
          </el-input-number>
        </el-form-item>
      </el-form>
    </el-tab-pane>

    <el-tab-pane label="数据源" name="DataSource">
      <el-form :model="dataSourceForm" label-width="90" label-position="left">
        <el-form-item label="数据源：">
          <el-select
            style="width: 200px"
            v-model="dataSourceForm.dataSourceType"
            placeholder="请选择数据源"
          >
            <el-option
              v-for="item in dataSourceOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-tab-pane>

    <el-tab-pane label="滤波器" name="Filters">
      <el-form :model="filterForm" label-width="90" label-position="left">
        <el-form-item label="滤波器：">
          <el-select
            style="width: 200px"
            v-model="filterForm.filterType"
            placeholder="请选择滤波器"
          >
            <el-option
              v-for="item in filterTypeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Alpha/BT：">
          <el-input-number style="width: 200px" v-model="filterForm.phaseModOffset">
          </el-input-number>
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }

  :deep(.el-tabs__nav) {
    float: none !important;
  }
  :deep(.el-tabs__nav .is-top) {
    width: 33.3% !important;
  }
</style>
