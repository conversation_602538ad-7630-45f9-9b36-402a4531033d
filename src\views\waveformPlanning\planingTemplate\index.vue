<script setup>
  import useList from '@/api/tool/filemanage/tableFunctionPro.js'
  import useDictStore from '@/store/modules/dict'
  import {
    allWavePlanTempList,
    deleteWaveTempPlan,
    exportWaveTempPlan
  } from '@/api/simulation/waveformPlan.js'
  import { ElMessage } from 'element-plus'
  import { cloneDeep } from 'lodash'
  import { displayUnitConversion } from '@/api/tool/filemanage'
  import { downloadFile } from '@/utils/fileDownload.js'

  const filterOption = ref({
    taskName: '', // 任务名称
    waveformType: '', // 组训人
    startTime: '', // 开始时间
    endTime: '' // 结束时间
  })
  const dictStore = useDictStore()
  const xTable = ref(null) // 表格实例
  const TMDialogFlag = ref(false) // 新增任务对话框标题
  const taskStatusOptions = ref([]) // 任务状态
  const TMId = ref(null) // 任务ID
  // 标准波形类型选项
  const standardWaveformOptions = reactive([])

  const {
    list,
    loading,
    curPage,
    size,
    total,
    timeSearch,
    loadData,
    deleteAll,
    selectChangeEvent
  } = useList(allWavePlanTempList, deleteWaveTempPlan, filterOption, xTable)

  const clearName = () => {
    filterOption.taskName = ''
    timeSearch(filterOption.value)
  }

  /**
   * 新增任务
   */
  const addMission = () => {
    TMDialogFlag.value = true
  }

  const editClick = row => {
    console.log(row)
    TMId.value = row.id
  }

  // 导出训练任务数据
  const exportFile = async () => {
    await exportWaveTempPlan().then(res => {
      downloadFile(res, '规划模板')
    })
  }

  //  获取 任务状态 字典数据
  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'waveform_type')
      .forEach(item => {
        standardWaveformOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  onMounted(async () => {
    await getDictionaryData()
  })
</script>
<template>
  <CuTitle title="规划模板" />
  <vxe-toolbar class="vxe-toolbar">
    <template #buttons>
      <vxe-select
        placeholder="请选择模板类型"
        v-model="filterOption.waveformType"
        @change="timeSearch(filterOption)"
      >
        <vxe-option
          v-for="opt in standardWaveformOptions"
          :key="opt.value"
          :label="opt.label"
          :value="opt.value"
        ></vxe-option>
      </vxe-select>
      <vxe-input
        v-model="filterOption.taskName"
        type="search"
        placeholder="请输入模板名称"
        clearable
        @clear="clearName"
        @search-click="timeSearch(filterOption)"
      />

      <vxe-input
        v-model="filterOption.startTime"
        type="date"
        placeholder="请选择起始时间"
        class="time-button"
        clearable
      />
      <p class="interval-p"> ~ </p>
      <vxe-input
        v-model="filterOption.endTime"
        clearable
        type="date"
        placeholder="请选择截止时间"
      />
      <vxe-button status="primary" content="查询" @click="timeSearch(filterOption)" />
      <vxe-button status="primary" content="新增" @click="addMission" />
      <vxe-button status="primary" content="导出" @click="exportFile" />
      <vxe-button status="primary" content="删除" @click="deleteAll" />
    </template>
  </vxe-toolbar>
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    height="680"
    :data="list"
    :loading="loading"
    :keep-source="true"
    :checkbox-config="{ labelField: 'listId' }"
    :row-config="{ isCurrent: true, isHover: true }"
    @checkbox-change="selectChangeEvent"
    @checkbox-all="selectChangeEvent"
  >
    <vxe-column type="checkbox" title="序号" width="90" fixed="left" align="center" />
    <vxe-column
      field="modelName"
      title="模板名称"
      width="140"
      fixed="left"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column field="createTime" title="创建时间" width="180" align="center" />
    <vxe-column field="createUser" title="标准波形" width="100" align="center" />
    <vxe-column field="trainCount" title="起始点" width="100" align="center" />
    <vxe-column field="trainEndCount" title="终止点" width="100" align="center" />
    <vxe-column field="avgScore" title="起始幅度" width="100" align="center" />
    <vxe-column field="taskFile" title="终止幅度" width="160" align="center" />
    <vxe-column field="taskFile" title="幅度偏移" width="160" align="center" />
    <vxe-column field="taskFile" title="相位偏移" width="160" align="center" />
    <vxe-column
      field="taskName"
      title="模板描述"
      width="140"
      fixed="left"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />

    <vxe-column field="操作" title="操作" width="auto" fixed="right" align="center">
      <template #default="{ row }">
        <vxe-button status="primary" content="波形生成" />
        <vxe-button status="primary" content="修改" />
      </template>
    </vxe-column>
  </vxe-table>

  <!-- 分页 -->
  <p>
    <vxe-pager
      v-model:current-page="curPage"
      v-model:page-size="size"
      class="vxe-page"
      perfect
      :total="total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total'
      ]"
    />
  </p>
</template>

<style lang="scss" scoped>
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }
</style>
