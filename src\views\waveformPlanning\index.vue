<script setup>
  import useDictStore from '@/store/modules/dict'
  import CubicSpline from 'cubic-spline'
  import Decimal from 'decimal.js'
  import { cloneDeep } from 'lodash'
  import { addWavePlan, readWavePlan } from '@/api/simulation/waveformPlan.js'
  import WaveFileSelectDialog from './components/waveFileSelectDialog.vue'
  import { ElMessage } from 'element-plus'

  /** ------------------ 表单 & 数据 ------------------ **/
  const dictStore = useDictStore()
  const form = reactive({
    standardWaveform: '0',
    startPoint: 0,
    endPoint: 2047,
    startAmplitude: 0,
    endAmplitude: 0,
    phaseShift: 0,
    amplitudeShift: 0
  })
  const WFPDialogFlag = ref(false) // 学员对话框标题
  const standardWaveformOptions = reactive([]) // 标准波形类型选项
  const undoStack = [] // 定义撤销栈，记录操作快照
  const loading = ref(false) // 加载状态

  // 两条波形数据（保存备用）
  let waveformData1 = { xData: [], yData: [] }
  let waveformData2 = { xData: [], yData: [] }
  let currentWaveform = { xData: [], yData: [] } // 主图当前波形数据
  /** ------------------ 拖拽相关 ------------------ **/
  let isDragging = false // 是否正在拖拽
  let draggingIndex = null // 正在拖拽的控制点索引
  let mainCanvas = null // 主图画布元素

  /** ------------------ 坐标系边距 & 范围 ------------------ **/
  // 为绘制坐标轴与刻度预留边距
  const margin = { top: 20, left: 50, right: 20, bottom: 40 }
  // y 范围固定在 [-1, 1]
  const yMin = -1,
    yMax = 1
  // x 范围由表单控制
  const getXRange = () => ({
    start: Number(form.startPoint),
    end: Number(form.endPoint)
  })

  let controlPoints = generateControlPoints() // 初始化控制点
  let hoveredIndex = null // 增加一个变量来记录当前悬停的控制点
  const DRAG_THRESHOLD = 8 // 鼠标与数据点的距离阈值（单位：像素）
  const NUM_CONTROL_POINTS = 20 // 特征点数量
  const MIN_X_GAP = ((getXRange().end - getXRange().start) / NUM_CONTROL_POINTS) * 0.5

  // 初始化控制点
  function generateControlPoints() {
    const { start, end } = getXRange()
    const step = (end - start) / (NUM_CONTROL_POINTS - 1)
    const xValues = []
    const yValues = []

    for (let i = 0; i < NUM_CONTROL_POINTS; i++) {
      const x = Math.round(start + i * step)
      xValues.push(x)
      yValues.push(Math.sin(x * ((2 * Math.PI) / 2048))) // 示例：正弦波初始化
    }
    return { xValues, yValues }
  }

  /** ------------------ 波形数据生成 ------------------ **/
  const generateWaveData = waveType => {
    const xValues = []
    const yValues = []
    let maxSignal = -Infinity,
      minSignal = Infinity
    const xStart = Number(form.startPoint)
    const xEnd = Number(form.endPoint)
    for (let i = xStart; i <= xEnd; i++) {
      xValues.push(i)
      switch (waveType) {
        case '0':
          yValues.push(Math.sin(i * ((2 * Math.PI) / 2048)))
          break
        case '1':
          yValues.push(i % 2048 < 1024 ? 1 : -1)
          break
        case '2':
          yValues.push(2 * Math.abs(2 * (i / 2048 - Math.floor(i / 2048 + 0.5))) - 1)
          break
        case '3': {
          const T = 102.4,
            A = 2
          const yStep = Math.floor((A * T * 100) / 2048) / 100
          yValues.push(Math.floor((i + T / 2) / T) * yStep - 1)
          break
        }
        case '4':
          yValues.push(Math.random() * 2 - 1)
          break
        case '5':
          yValues.push(Math.max(Math.sin(i * ((2 * Math.PI) / 2048)), 0))
          break
        case '6':
          yValues.push(Math.abs(Math.sin(i * ((2 * Math.PI) / 2048))))
          break
        case '7':
          yValues.push(Math.log10(i + 1) / Math.log10(2048))
          break
        case '8':
          yValues.push(-Math.log10(i + 1) / Math.log10(2048))
          break
        case '9': {
          let signal = 0
          for (let n = 1; n <= 4; n++) {
            signal += Math.sin(i * ((2 * Math.PI * n) / 2048)) / 4
          }
          yValues.push(signal)
          if (signal > maxSignal) maxSignal = signal
          if (signal < minSignal) minSignal = signal
          break
        }
        case '10': {
          let x = (i - 1024) / 1024,
            sigma = 0.4
          yValues.push(
            Math.sin(i * ((2 * Math.PI) / 2048)) *
              Math.exp(-Math.pow(x, 2) / (2 * Math.pow(sigma, 2)))
          )
          break
        }
        case '11':
          yValues.push(0)
          break
        default:
          yValues.push(0)
      }
    }
    // 多音频归一化
    if (waveType === '多音频') {
      yValues.forEach((val, index) => {
        yValues[index] = -1 + (2 * (val - minSignal)) / (maxSignal - minSignal)
      })
    }
    // 限制 y 值在 [-1,1]
    yValues.forEach((val, index) => {
      yValues[index] = Math.max(Math.min(val, 1), -1)
    })
    return { xValues, yValues }
  }

  //  获取 任务状态 字典数据
  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'waveform_type')
      .forEach(item => {
        standardWaveformOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  /** ------------------ 坐标转换 ------------------ **/
  /**
   *  将数据 x 映射到画布像素坐标 sx
   * @param xVal 数据 x
   * @param width 画布宽度
   * @param xStart x 范围起点
   * @param xEnd x 范围终点
   * @returns {number} 画布像素坐标 sx
   */
  const dataToScreenX = (xVal, width, xStart, xEnd) => {
    const plotWidth = width - margin.left - margin.right
    return margin.left + ((xVal - xStart) / (xEnd - xStart)) * plotWidth
  }
  /**
   * 将数据 y 映射到画布像素坐标 sy
   * @param yVal 数据 y
   * @param height
   */
  const dataToScreenY = (yVal, height) => {
    const plotHeight = height - margin.top - margin.bottom
    return margin.top + ((yMax - yVal) / (yMax - yMin)) * plotHeight
  }

  /** 将画布像素 (sx, sy) 反算为数据坐标 (xVal, yVal) 以支持 2D 拖拽 **/
  const screenToDataXY = (sx, sy, width, height) => {
    const { start: xStart, end: xEnd } = getXRange()
    const plotWidth = width - margin.left - margin.right
    const plotHeight = height - margin.top - margin.bottom
    const px = sx - margin.left
    const py = sy - margin.top
    let xVal = xStart + (px / plotWidth) * (xEnd - xStart)
    let yVal = yMax - (py / plotHeight) * (yMax - yMin)
    xVal = Math.max(Math.min(xVal, xEnd), xStart)
    yVal = Math.max(Math.min(yVal, yMax), yMin)
    return { xVal, yVal }
  }

  /**
   * 绘制波形
   * @param canvas 画布
   * @param xData x 轴数据
   * @param yData y 轴数据
   */
  const drawWaveform = (canvas, xData, yData) => {
    const ctx = canvas.getContext('2d')
    const width = canvas.width
    const height = canvas.height
    ctx.fillStyle = '#fff'
    ctx.fillRect(0, 0, width, height)
    drawGrid(ctx, width, height)
    drawAxes(ctx, width, height)
    const { start: xStart, end: xEnd } = getXRange()
    ctx.strokeStyle = '#000'
    ctx.lineWidth = 1.5
    ctx.beginPath()
    xData.forEach((x, i) => {
      const y = yData[i]
      if (y >= yMin && y <= yMax && x >= xStart && x <= xEnd) {
        const sx = dataToScreenX(x, width, xStart, xEnd)
        const sy = dataToScreenY(y, height)
        if (i === 0 || yData[i - 1] < yMin || yData[i - 1] > yMax) {
          ctx.moveTo(sx, sy)
        } else {
          ctx.lineTo(sx, sy)
        }
      } else {
        ctx.stroke()
        ctx.beginPath()
      }
    })
    ctx.stroke()
    // 绘制控制点（在主图中）
    if (canvas.id === 'mainWaveformCanvas') {
      controlPoints.xValues.forEach((x, i) => {
        const y = controlPoints.yValues[i]
        if (y >= yMin && y <= yMax && x >= xStart && x <= xEnd) {
          const sx = dataToScreenX(x, width, xStart, xEnd)
          const sy = dataToScreenY(y, height)
          ctx.beginPath()
          // 如果当前点被鼠标悬停，则绘制大一些或更换颜色
          if (i === hoveredIndex) {
            ctx.arc(sx, sy, 6, 0, 2 * Math.PI)
            ctx.fillStyle = '#00f' // 高亮颜色
          } else {
            ctx.arc(sx, sy, 4, 0, 2 * Math.PI)
            ctx.fillStyle = '#FF0000'
          }
          ctx.fill()
        }
      })
    }
  }

  /** 绘制网格：x 方向每 100 单位，y 方向每 0.2 单位 **/
  function drawGrid(ctx, width, height) {
    ctx.lineWidth = 1
    ctx.strokeStyle = '#006600'
    const { start: xStart, end: xEnd } = getXRange()
    const plotWidth = width - margin.left - margin.right
    const plotHeight = height - margin.top - margin.bottom
    for (let xVal = xStart + 100; xVal < xEnd; xVal += 100) {
      const sx = dataToScreenX(xVal, width, xStart, xEnd)
      ctx.beginPath()
      ctx.moveTo(sx, margin.top)
      ctx.lineTo(sx, margin.top + plotHeight)
      ctx.stroke()
    }
    for (let yVal = yMin + 0.2; yVal < yMax + 1e-9; yVal += 0.2) {
      const sy = dataToScreenY(yVal, height)
      ctx.beginPath()
      ctx.moveTo(margin.left, sy)
      ctx.lineTo(margin.left + plotWidth, sy)
      ctx.stroke()
    }
  }

  /** 绘制坐标轴与刻度 **/
  function drawAxes(ctx, width, height) {
    ctx.lineWidth = 2
    ctx.strokeStyle = '#000'
    ctx.fillStyle = '#000'
    ctx.font = '14px Arial'
    const plotWidth = width - margin.left - margin.right
    const plotHeight = height - margin.top - margin.bottom
    const { start: xStart, end: xEnd } = getXRange()
    ctx.beginPath()
    ctx.moveTo(margin.left, margin.top + plotHeight)
    ctx.lineTo(margin.left + plotWidth, margin.top + plotHeight)
    ctx.stroke()
    ctx.beginPath()
    ctx.moveTo(margin.left, margin.top)
    ctx.lineTo(margin.left, margin.top + plotHeight)
    ctx.stroke()
    const xStep = 400
    ctx.textAlign = 'center'
    ctx.textBaseline = 'top'
    for (let xVal = xStart; xVal <= xEnd; xVal += xStep) {
      const sx = dataToScreenX(xVal, width, xStart, xEnd)
      const sy = margin.top + plotHeight
      ctx.beginPath()
      ctx.moveTo(sx, sy)
      ctx.lineTo(sx, sy + 5)
      ctx.stroke()
      ctx.fillText(`${xVal}`, sx, sy + 8)
    }
    ctx.fillText('X-Points', margin.left + plotWidth / 2, margin.top + plotHeight + 25)
    const yStep = 0.2
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle'
    for (let yVal = yMin; yVal <= yMax + 1e-9; yVal += yStep) {
      const sy = dataToScreenY(yVal, height)
      ctx.beginPath()
      ctx.moveTo(margin.left, sy)
      ctx.lineTo(margin.left - 5, sy)
      ctx.stroke()
      ctx.fillText(yVal.toFixed(1), margin.left - 8, sy)
    }
    ctx.save()
    ctx.translate(margin.left - 30, margin.top + plotHeight / 2)
    ctx.rotate(-Math.PI / 2)
    ctx.textAlign = 'center'
    ctx.textBaseline = 'bottom'
    ctx.fillText('y-Amplitude', 0, 0)
    ctx.restore()
  }

  /** ------------------ 渲染主图/副图 ------------------ **/
  /**
   * 渲染波形图
   * @param xData x 轴数据
   * @param yData y 轴数据
   */
  const renderChart = (xData, yData) => {
    currentWaveform = { xData: xData.slice(), yData: yData.slice() }
    if (mainCanvas) {
      drawWaveform(mainCanvas, xData, yData)
    }
  }

  /**
   *  渲染波形图
   * @param id canvas 元素 id
   * @param xData  x 轴数据，一般为整数，一般为波形采样点数
   * @param yData  y 轴数据，一般为波形幅度
   */
  const renderChartByID = (id, xData, yData) => {
    const canvas = document.getElementById(id)
    if (canvas) {
      drawWaveform(canvas, xData, yData)
    }
  }

  /** ------------------ 功能按钮回调 ------------------ **/
  // 保存状态快照
  const pushUndoState = () => {
    // 保存 currentWaveform、controlPoints，若需要也可保存 form 中部分值
    undoStack.push({
      currentWaveform: cloneDeep(currentWaveform),
      controlPoints: cloneDeep(controlPoints)
    })
  }

  /**
   * 打开波形文件：读取波形数据，并更新图表等
   * @param selectRow 波形文件行数据，包含文件名等信息
   */
  const openFile = async selectRow => {
    loading.value = true
    await readWavePlan({ fileName: selectRow.fileName }).then(res => {
      if (res.code === 200) {
        loading.value = false
        ElMessage({
          message: '读取波形成功',
          type: 'success'
        })
        form.standardWaveform = res.data.standardWaveform
        form.startPoint = res.data.startPoint
        form.endPoint = res.data.endPoint
        form.startAmplitude = res.data.startAmplitude
        form.endAmplitude = res.data.endAmplitude
        form.phaseShift = res.data.phaseShift
        form.amplitudeShift = res.data.amplitudeShift
        // 清空撤销栈，或者根据需要保存历史记录
        undoStack.length = 0
        currentWaveform.yData = res.data.points
        // 根据新的 start/end 更新图表等
        updateControlPointsFromWaveform()
        renderChart(currentWaveform.xData, currentWaveform.yData)
      } else {
        ElMessage({
          message: res.msg,
          type: 'error'
        })
      }
    })
  }

  // 撤销操作：从撤销栈中弹出上一步状态，并恢复
  const undo = () => {
    if (undoStack.length === 1) {
      console.log('没有更多操作可以撤销')
      return
    }
    const prevState = undoStack.pop()
    // 恢复状态
    currentWaveform.xData = prevState.currentWaveform.xData.slice()
    currentWaveform.yData = prevState.currentWaveform.yData.slice()
    controlPoints.xValues = prevState.controlPoints.xValues.slice()
    controlPoints.yValues = prevState.controlPoints.yValues.slice()
    // 更新显示
    renderChart(currentWaveform.xData, currentWaveform.yData)
  }

  /**
   * 提交表单：根据标准波形生成波形数据，并更新图表
   */
  const onSubmit = () => {
    pushUndoState()
    const { xValues, yValues } = generateWaveData(form.standardWaveform)
    currentWaveform = { xData: xValues.slice(), yData: yValues.slice() }
    updateControlPointsFromWaveform() // 同步控制点
    renderChart(currentWaveform.xData, currentWaveform.yData)
  }

  /**
   * 重置波形：恢复到初始状态，撤销栈清空
   */
  const reset = () => {
    undoStack.value = []
    onSubmit()
  }

  // 翻转波形：y轴翻转，但不改变相位
  const flipFun = () => {
    pushUndoState()
    currentWaveform.yData = currentWaveform.yData.map(val => new Decimal(val).neg().toNumber())
    updateControlPointsFromWaveform()
    renderChart(currentWaveform.xData, currentWaveform.yData)
  }

  /**
   * 根据当前波形数据，更新控制点（X轴位置不变，Y轴精确同步）
   */
  const updateControlPointsFromWaveform = () => {
    const { start, end } = getXRange()
    const step = (end - start) / (NUM_CONTROL_POINTS - 1)
    controlPoints.xValues = []
    controlPoints.yValues = []

    for (let i = 0; i < NUM_CONTROL_POINTS; i++) {
      const x = Math.round(start + i * step)
      controlPoints.xValues.push(x)
      // 根据当前真实波形数据，精确地同步y值
      controlPoints.yValues.push(currentWaveform.yData[x - start])
    }
  }

  /**
   * 应用相位偏移
   */
  const applyPhaseShift = () => {
    pushUndoState()
    const shift = parseInt(form.phaseShift) || 0
    if (!currentWaveform.yData.length) return
    const len = currentWaveform.yData.length
    const newY = currentWaveform.yData.slice()
    if (shift > 0) {
      const tail = newY.splice(-shift, shift)
      newY.unshift(...tail)
    } else if (shift < 0) {
      const head = newY.splice(0, Math.abs(shift))
      newY.push(...head)
    }
    currentWaveform.yData = newY.slice()
    // 控制点相位同步移动（X轴）
    const { start, end } = getXRange()
    controlPoints.xValues = controlPoints.xValues.map(x => {
      let shiftedX = x + shift
      if (shiftedX > end) shiftedX -= len
      if (shiftedX < start) shiftedX += len
      return shiftedX
    })
    controlPoints.yValues = controlPoints.xValues.map(x => currentWaveform.yData[x - start])
    renderChart(currentWaveform.xData, currentWaveform.yData)
  }

  /**
   * 应用幅度偏移
   */
  const applyAmplitudeShift = () => {
    pushUndoState()
    const shift = parseFloat(form.amplitudeShift) || 0
    if (!currentWaveform.yData.length) return
    currentWaveform.yData = currentWaveform.yData.map(val => val + shift)
    updateControlPointsFromWaveform()
    renderChart(currentWaveform.xData, currentWaveform.yData)
  }

  /**
   * 设置波形数据到全局变量，以便在其他地方使用
   * @param id 波形画布的ID，用于区分两个波形画布
   */
  const setWaveform = id => {
    if (currentWaveform.xData.length && currentWaveform.yData.length) {
      if (id === '1') {
        waveformData1 = {
          xData: currentWaveform.xData.slice(),
          yData: currentWaveform.yData.slice()
        }
        renderChartByID('waveformCanvas1', currentWaveform.xData, currentWaveform.yData)
      } else if (id === '2') {
        waveformData2 = {
          xData: currentWaveform.xData.slice(),
          yData: currentWaveform.yData.slice()
        }
        renderChartByID('waveformCanvas2', currentWaveform.xData, currentWaveform.yData)
      }
    } else {
      const { xValues, yValues } = generateWaveData(form.standardWaveform)
      if (id === '1') {
        waveformData1 = { xData: xValues.slice(), yData: yValues.slice() }
        renderChartByID('waveformCanvas1', xValues, yValues)
      } else if (id === '2') {
        waveformData2 = { xData: xValues.slice(), yData: yValues.slice() }
        renderChartByID('waveformCanvas2', xValues, yValues)
      }
    }
  }

  /**
   * 波形相加
   */
  const waveformAddition = () => {
    const yDataResult = waveformData1.yData.map((val, idx) => val + waveformData2.yData[idx])
    currentWaveform = { xData: waveformData1.xData.slice(), yData: yDataResult }
    updateControlPointsFromWaveform() // 新增这一行！
    renderChart(currentWaveform.xData, currentWaveform.yData)
  }

  /**
   * 波形相减
   */
  const waveformSubtraction = () => {
    const yDataResult = waveformData1.yData.map((val, idx) => val - waveformData2.yData[idx])
    currentWaveform = { xData: waveformData1.xData.slice(), yData: yDataResult }
    updateControlPointsFromWaveform() // 新增这一行！
    renderChart(currentWaveform.xData, currentWaveform.yData)
  }

  /**
   * 波形相乘
   */
  const waveformMultiplication = () => {
    const yDataResult = waveformData1.yData.map((val, idx) => val * waveformData2.yData[idx])
    currentWaveform = { xData: waveformData1.xData.slice(), yData: yDataResult }
    updateControlPointsFromWaveform() // 新增这一行！
    renderChart(currentWaveform.xData, currentWaveform.yData)
  }

  // 保存波形数据到数据库
  const saveWave = async () => {
    const waveYData = cloneDeep(currentWaveform.yData).map(val => Number(val.toFixed(6)))
    const waveForm = {
      points: waveYData,
      ...form
    }
    await addWavePlan(waveForm).then(res => {
      if (res.code === 200) {
        ElMessage({ type: 'success', message: '文件保存成功' })
      } else {
        ElMessage({ type: 'error', message: res.msg })
      }
    })
  }

  /** ------------------ 鼠标hover优化体验 ------------------   */
  function handleMouseCursor(e) {
    const rect = mainCanvas.getBoundingClientRect()
    const mouseX = e.clientX - rect.left
    const mouseY = e.clientY - rect.top
    const idx = findClosestControlPoint(mouseX, mouseY, rect)
    // 更新 hoveredIndex 以便动态渲染
    hoveredIndex = idx >= 0 ? idx : null
    mainCanvas.style.cursor = idx >= 0 ? 'pointer' : 'default'
  }

  /** ------------------ 2D 拖拽事件处理 ------------------ **/

  // 修改 findClosestControlPoint，在 onMouseMove 中更新 hoveredIndex
  function findClosestControlPoint(mouseX, mouseY, canvasRect) {
    let minDist = Infinity
    let closestIndex = -1
    const width = canvasRect.width
    const height = canvasRect.height
    const { start: xStart, end: xEnd } = getXRange()
    for (let i = 0; i < controlPoints.xValues.length; i++) {
      const sx =
        margin.left +
        ((controlPoints.xValues[i] - xStart) / (xEnd - xStart)) *
          (width - margin.left - margin.right)
      const sy =
        margin.top +
        ((yMax - controlPoints.yValues[i]) / (yMax - yMin)) * (height - margin.top - margin.bottom)
      const dx = sx - mouseX
      const dy = sy - mouseY
      const dist = Math.sqrt(dx * dx + dy * dy)
      if (dist < minDist) {
        minDist = dist
        closestIndex = i
      }
    }
    return minDist < DRAG_THRESHOLD ? closestIndex : -1
  }

  const onMouseDown = e => {
    const rect = mainCanvas.getBoundingClientRect()
    const mouseX = e.clientX - rect.left
    const mouseY = e.clientY - rect.top
    const idx = findClosestControlPoint(mouseX, mouseY, rect)
    if (idx >= 0) {
      pushUndoState() // 记录拖拽前的状态
      draggingIndex = idx
      isDragging = true
      mainCanvas.style.cursor = 'grabbing'
    }
  }

  // 修改 onMouseMove 逻辑，优先更新 hoveredIndex
  const onMouseMove = e => {
    if (!isDragging) {
      handleMouseCursor(e)
    }
    if (!isDragging || draggingIndex == null) return
    const rect = mainCanvas.getBoundingClientRect()
    const mouseX = e.clientX - rect.left
    const mouseY = e.clientY - rect.top
    const { xVal, yVal } = screenToDataXY(mouseX, mouseY, rect.width, rect.height)

    // 限制X轴拖拽范围
    const minX =
      draggingIndex === 0 ? getXRange().start : controlPoints.xValues[draggingIndex - 1] + MIN_X_GAP
    const maxX =
      draggingIndex === controlPoints.xValues.length - 1
        ? getXRange().end
        : controlPoints.xValues[draggingIndex + 1] - MIN_X_GAP

    controlPoints.xValues[draggingIndex] = Math.round(Math.max(minX, Math.min(maxX, xVal)))
    controlPoints.yValues[draggingIndex] = Math.max(-1, Math.min(1, yVal))

    // 实时插值波形
    const spline = new CubicSpline(controlPoints.xValues, controlPoints.yValues)
    const { start, end } = getXRange()
    const tempX = []
    const tempY = []
    for (let x = start; x <= end; x++) {
      tempX.push(x)
      tempY.push(Math.max(-1, Math.min(1, spline.at(x))))
    }
    renderChart(tempX, tempY)
  }

  const onMouseUp = () => {
    if (!isDragging) return
    isDragging = false
    draggingIndex = null
    mainCanvas.style.cursor = 'default'
    const { start, end } = getXRange()
    const spline = new CubicSpline(controlPoints.xValues, controlPoints.yValues)
    currentWaveform.xData = []
    currentWaveform.yData = []
    for (let x = start; x <= end; x++) {
      currentWaveform.xData.push(x)
      currentWaveform.yData.push(spline.at(x))
    }
    renderChart(currentWaveform.xData, currentWaveform.yData)
  }

  onMounted(() => {
    getDictionaryData()
    mainCanvas = document.getElementById('mainWaveformCanvas')
    // 获取实际显示尺寸并设置 canvas 属性尺寸（防止缩放偏差）
    const rect = mainCanvas.getBoundingClientRect()
    mainCanvas.width = rect.width
    mainCanvas.height = rect.height
    onSubmit() // 初始生成波形
    if (mainCanvas) {
      mainCanvas.addEventListener('mousedown', onMouseDown)
      mainCanvas.addEventListener('mousemove', onMouseMove)
      mainCanvas.addEventListener('mouseup', onMouseUp)
      mainCanvas.addEventListener('mouseleave', onMouseUp)
    }
    // 初始化波形图1、图2（自定义折线）
    const { xValues, yValues } = generateWaveData('自定义折线')
    renderChartByID('waveformCanvas1', xValues, yValues)
    renderChartByID('waveformCanvas2', xValues, yValues)
    waveformData1 = waveformData2 = { xData: xValues.slice(), yData: yValues.slice() }
  })
</script>

<template>
  <cu-title title="波形生成"></cu-title>
  <el-row v-loading="loading" :gutter="30">
    <!-- 左侧参数表单 -->
    <el-col class="mt-11" :span="5">
      <el-form :model="form" label-width="auto">
        <el-form-item label="标准波形：">
          <el-select style="width: 100%" v-model="form.standardWaveform" placeholder="请选择波形">
            <el-option
              v-for="item in standardWaveformOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="起始点：">
          <el-input v-model="form.startPoint" />
        </el-form-item>
        <el-form-item label="终止点：">
          <el-input v-model="form.endPoint" />
        </el-form-item>
        <el-form-item label="起始幅度：">
          <el-input :disabled="true" v-model="form.startAmplitude" />
        </el-form-item>
        <el-form-item label="终止幅度：">
          <el-input :disabled="true" v-model="form.endAmplitude" />
        </el-form-item>
      </el-form>
      <div class="text-center">
        <cu-button @click="onSubmit" content="产生波形"></cu-button>
      </div>
    </el-col>

    <!-- 右侧主图及功能按钮 -->
    <el-col :span="19">
      <el-row :gutter="30">
        <el-col :span="20">
          <div class="text-right mb-2">
            <cu-button content="从文件中打开" @click="WFPDialogFlag = true"></cu-button>
            <cu-button content="撤销" @click="undo"></cu-button>
            <cu-button content="重做" @click="reset"></cu-button>
            <cu-button content="翻转" @click="flipFun"></cu-button>
            <cu-button content="保存波形" @click="saveWave"></cu-button>
          </div>
          <!-- 主波形图 -->
          <canvas id="mainWaveformCanvas" class="w-full" width="800" height="400"></canvas>
        </el-col>

        <!-- 幅度移动 & 相位移动 -->
        <el-col :span="4" style="display: flex; flex-direction: column; justify-content: center">
          <div class="mb-5">
            <span> 幅度移动 </span>
            <div class="flex items-end justify-between mt-2">
              <el-input
                style="width: 100px"
                v-model="form.amplitudeShift"
                placeholder="输入幅度偏移"
              />
              <cu-button content="保存" @click="applyAmplitudeShift"></cu-button>
            </div>
          </div>
          <div>
            <span> 相位移动 </span>
            <div class="flex items-end justify-between mt-2">
              <el-input style="width: 100px" v-model="form.phaseShift" placeholder="输入点数偏移">
                <template #append>
                  <span>°</span>
                </template>
              </el-input>
              <cu-button content="保存" @click="applyPhaseShift"></cu-button>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 波形1/波形2 与波形运算 -->
      <el-row :gutter="30">
        <el-col :span="20">
          <el-row :gutter="20">
            <el-col :span="12" class="text-center">
              <cu-button class="my-5" content="设为波形一" @click="setWaveform('1')"></cu-button>
              <canvas id="waveformCanvas1" class="w-full" width="800" height="350"></canvas>
            </el-col>
            <el-col :span="12" class="text-center">
              <cu-button class="my-5" content="设为波形二" @click="setWaveform('2')"></cu-button>
              <canvas id="waveformCanvas2" class="w-full" width="800" height="350"></canvas>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          class="waveformAlgorithm"
          :span="4"
          style="
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          "
        >
          <cu-button content="波形相加" @click="waveformAddition"></cu-button>
          <cu-button content="波形相减" @click="waveformSubtraction"></cu-button>
          <cu-button content="波形相乘" @click="waveformMultiplication"></cu-button>
        </el-col>
      </el-row>
    </el-col>
  </el-row>

  <WaveFileSelectDialog v-if="WFPDialogFlag" v-model="WFPDialogFlag" @upward-fun="openFile" />
</template>

<style scoped lang="scss">
  canvas {
    border: 1px solid #ccc;
    background-color: #fff; /* 白色背景，此处可根据需要调整 */
  }

  /* 让“波形运算”按钮垂直排列 */
  :deep(.waveformAlgorithm > .vxe-button + .vxe-button) {
    margin-left: 0 !important;
    margin-top: 20px !important;
  }
</style>
