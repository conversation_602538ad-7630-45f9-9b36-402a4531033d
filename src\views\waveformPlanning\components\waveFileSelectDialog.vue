<script setup>
  import { allWavePlan } from '@/api/simulation/waveformPlan.js'
  import useList from '@/api/tool/filemanage/tableFunctionPro.js'
  import { ElMessage } from 'element-plus'

  const emit = defineEmits(['UpwardFun'])
  const xTable = ref(null) // 表格实例
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const selectRow = ref(null)

  const { list, loading, curPage, size, total } = useList(allWavePlan, null, {}, xTable)

  const radioChangeEvent = ({ row }) => {
    selectRow.value = row
  }

  const confirm = () => {
    if (!selectRow.value) {
      return ElMessage.error('请选择波形文件')
    }
    emit('UpwardFun', selectRow.value)
    dialogTableVisible.value = false
  }

  const handleClose = () => {
    dialogTableVisible.value = false
  }

  onMounted(async () => {})
</script>

<template>
  <el-dialog
    class="px-10"
    v-model="dialogTableVisible"
    title="波形文件选择"
    align="center"
    width="800"
    @closed="handleClose"
  >
    <vxe-table
      ref="xTable"
      border
      stripe
      size="medium"
      height="380"
      :data="list"
      :loading="loading"
      :checkbox-config="{ labelField: 'listId' }"
      :row-config="{ isCurrent: true, isHover: true }"
      @radio-change="radioChangeEvent"
    >
      <vxe-column type="radio" title="序号" width="90" fixed="left" align="center" />
      <vxe-column field="fileName" title="文件名称" width="200" fixed="left" align="center" />
    </vxe-table>
    <!-- 分页 -->
    <p>
      <vxe-pager
        v-model:current-page="curPage"
        v-model:page-size="size"
        class="vxe-page"
        perfect
        :total="total"
        :page-sizes="[10, 20, 50, 100, 200, 500]"
        :layouts="[
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'Sizes',
          'FullJump',
          'Total'
        ]"
      />
    </p>
    <template #footer>
      <div class="text-center">
        <vxe-button @click="handleClose">取消</vxe-button>
        <vxe-button status="primary" @click="confirm">确认</vxe-button>
      </div>
    </template>
  </el-dialog>
</template>
