<script setup>
  const form = ref({
    enable: false,
    fadingCriterion: '0', //衰落标准
    fadingMod: '1' //衰落模式
  })

  const declineStandardOptions = [
    { label: '静态衰落', value: '0' },
    { label: '生灭场景', value: '1' },
    { label: '动态场景', value: '2' },
    { label: '高铁场景', value: '3' }
  ]

  const declineModeOptions = [
    { label: '静态', value: '0' },
    { label: '纯多普勒', value: '1' },
    { label: '瑞利', value: '2' },
    { label: '莱斯', value: '3' },
    { label: '纯相移', value: '4' },
    { label: '高斯1', value: '5' },
    { label: '高斯2', value: '6' },
    { label: '高斯DAB', value: '7' },
    { label: '高斯多普勒', value: '8' },
    { label: '高斯（0.08 fd）', value: '9' },
    { label: '高斯（0.1 fd）', value: '10' }
  ]

  const setFormData = data => {
    form.value = { ...data }
  }

  defineExpose({
    form,
    setFormData
  })
</script>

<template>
  <el-form :model="form" label-width="auto" label-position="left">
    <el-form-item label="">
      <el-checkbox v-model="form.enable">启用</el-checkbox>
    </el-form-item>
    <el-form-item label="衰落标准：">
      <el-select style="width: 200px" v-model="form.fadingCriterion">
        <el-option
          v-for="item in declineStandardOptions"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="衰落模式：">
      <el-select style="width: 200px" v-model="form.fadingMod">
        <el-option
          v-for="item in declineModeOptions"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
  :deep(.cusBtnNoise) {
    width: 33%;
  }
  :deep(.cusBtn) {
    width: 50%;
  }
  :deep(.el-radio-button--large .el-radio-button__inner) {
    width: 100%;
  }
</style>
