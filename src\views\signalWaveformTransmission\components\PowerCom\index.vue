<script setup>
  import PowerSettings from './PowerSettings.vue'
  import AttenuationControl from './AttenuationControl.vue'
  import LoopControl from './LoopControl.vue'
  import AmplitudeStabMethod from './AmplitudeStabMethod.vue'
  import ALCBandwidth from './ALCBandwidth.vue'
  import PowerSweep from './PowerSweep.vue'

  const activeName = ref('PowerSettings')
  const PowerSettingsRef = ref(null)
  const AttenuationControlRef = ref(null)
  const LoopControlRef = ref(null)
  const AmplitudeStabMethodRef = ref(null)
  const ALCBandwidthRef = ref(null)
  const PowerSweepRef = ref(null)

  const handleClick = (tab, event) => {
    console.log(tab, event)
    PowerSettingsRef.value.reset()
    AttenuationControlRef.value.reset()
    LoopControlRef.value.reset()
    AmplitudeStabMethodRef.value.reset()
    ALCBandwidthRef.value.reset()
    PowerSweepRef.value.reset()
  }

  defineExpose({
    PowerSettingsRef,
    AttenuationControlRef,
    LoopControlRef,
    AmplitudeStabMethodRef,
    ALCBandwidthRef,
    PowerSweepRef
  })
</script>

<template>
  <el-tabs class="h-full" v-model="activeName" type="border-card" @tab-click="handleClick">
    <el-tab-pane label="功率设置" name="PowerSettings">
      <PowerSettings ref="PowerSettingsRef" />
    </el-tab-pane>
    <el-tab-pane label="衰减控制" name="AttenuationControl">
      <AttenuationControl ref="AttenuationControlRef" />
    </el-tab-pane>
    <el-tab-pane label="环路控制" name="LoopControl">
      <LoopControl ref="LoopControlRef" />
    </el-tab-pane>
    <el-tab-pane label="稳幅方式" name="AmplitudeStabMethod">
      <AmplitudeStabMethod ref="AmplitudeStabMethodRef" />
    </el-tab-pane>
    <el-tab-pane label="ALC带宽" name="ALCBandwidth">
      <ALCBandwidth ref="ALCBandwidthRef" />
    </el-tab-pane>
    <el-tab-pane label="功率扫描" name="PowerSweep">
      <PowerSweep ref="PowerSweepRef" />
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
  :deep(.el-tabs__nav) {
    float: none !important;
  }
  :deep(.el-tabs__nav .is-top) {
    width: 33.3% !important;
  }
</style>
