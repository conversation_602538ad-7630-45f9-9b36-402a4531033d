<script setup>
  import { useRouter } from 'vue-router'
  import useList from '@/api/tool/filemanage/tableFunctionPro'
  import { downloadFile } from '@/utils/fileDownload.js'
  import {
    allSignalSimulationTaskList,
    deleteSignalSimulationTask
  } from '@/api/simulation/signalSimulation.js'

  const filterOption = ref({
    taskName: '', //任务名称
    createBy: '', //参训人
    startTime: '', //开始时间
    endTime: '' //结束时间
  })
  const xTable = ref(null)
  const router = useRouter()
  const {
    list,
    loading,
    curPage,
    size,
    total,
    timeSearch,
    deleteCurrentLine,
    deleteAll,
    selectChangeEvent
  } = useList(allSignalSimulationTaskList, deleteSignalSimulationTask, filterOption, xTable)

  const clearName = () => {
    filterOption.taskName = ''
    timeSearch(filterOption.value)
  }

  const clearUser = () => {
    filterOption.createBy = ''
    timeSearch(filterOption.value)
  }

  const exportRecord = async () => {
    await exportTRAnswer().then(res => {
      downloadFile(res, '信号模拟任务.xlsx')
    })
  }

  const handleEdit = row => {
    router.push({
      path: '/signalSimulation',
      query: { id: row.id }
    })
  }

  const handleEmission = row => {
    console.log('跳转信号波形发射')
    router.push({
      path: '/signalWaveformTransmission',
      query: { id: row.id }
    })
  }

  const addTask = () => {
    router.push({
      path: '/signalSimulation'
      // query: { type: 'add', data: JSON.stringify({}) }
    })
  }

  onMounted(async () => {})
</script>
<template>
  <CuTitle title="信号模拟任务" />
  <vxe-toolbar class="vxe-toolbar">
    <template #buttons>
      <vxe-input
        v-model="filterOption.taskName"
        type="search"
        placeholder="任务名称"
        clearable
        @clear="clearName"
      />
      <vxe-input
        v-model="filterOption.createBy"
        type="search"
        placeholder="制定人"
        clearable
        @clear="clearUser"
      />
      <!-- <vxe-input
        v-model="filterOption.startTime"
        type="date"
        placeholder="请选择开始时间"
        class="time-button"
        clearable
      />
      <p class="mx-2"> ~ </p>
      <vxe-input
        v-model="filterOption.endTime"
        type="date"
        placeholder="请选择结束时间"
        class="time-button"
        clearable
      /> -->
      <cu-button content="查询" @click="timeSearch(filterOption)" />
      <cu-button content="新增" @click="addTask" />
      <cu-button content="删除" @click="deleteAll" />
      <!-- <cu-button content="导出" /> -->
    </template>
  </vxe-toolbar>
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    height="680"
    :data="list"
    :loading="loading"
    :checkbox-config="{ labelField: 'listId' }"
    :row-config="{ isCurrent: true, isHover: true }"
    @checkbox-change="selectChangeEvent"
    @checkbox-all="selectChangeEvent"
  >
    <vxe-column type="checkbox" title="序号" width="90" fixed="left" align="center" />
    <vxe-column
      field="taskName"
      title="任务名称"
      width="240"
      fixed="left"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="taskDescribe"
      title="任务描述"
      width="240"
      fixed="left"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column field="createBy" title="制定人" width="100" align="center" />
    <vxe-column field="createTime" title="制定时间" width="180" align="center" />
    <vxe-column field="操作" title="操作" width="180" fixed="right" align="center">
      <template #default="{ row }">
        <cu-button content="编辑" @click="handleEdit(row)" />
        <cu-button content="发射" @click="handleEmission(row)" />
        <cu-button content="删除" @click="deleteCurrentLine(row)" />
      </template>
    </vxe-column>
  </vxe-table>
  <!-- 分页 -->
  <p>
    <vxe-pager
      v-model:current-page="curPage"
      v-model:page-size="size"
      class="vxe-page"
      perfect
      :total="total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total'
      ]"
    />
  </p>
</template>

<style lang="scss" scoped>
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }
</style>
