<script setup>
  import { useRoute, useRouter } from 'vue-router'
  import { getTRAnswer, checkTRAnswer, teaScore } from '@/api/simulation/trainingRecord.js'
  import { round } from 'lodash'
  import useDictStore from '@/store/modules/dict'
  import { ElMessage } from 'element-plus'

  const dictStore = useDictStore()
  const router = useRouter() // 路由实例
  const route = useRoute()
  const viewType = ref('') // 视图类型：check（检查）、edit（评分）
  // 识别结果表单
  const recognizeForm = ref({
    sigFeq: '',
    sigWidth: '',
    sigAmp: '',
    sigType: '',
    modType: '',
    isHop: '',
    isSpr: ''
  })
  // 标准结果表单
  const standardForm = ref({
    sigFeq: '',
    sigWidth: '',
    sigAmp: '',
    sigType: '',
    modType: '',
    isHop: '',
    isSpr: ''
  })
  const rltscore = ref(0) // 识别结果得分
  const opscore = ref(0) // 操作过程得分
  const operationRecord = ref()
  const adjustScoreFlag = ref(false) // 调整得分对话框标题
  const trainInfo = ref() // 训练记录信息

  const signalTypeOptions = reactive([])
  const operationStepsOptions = reactive([])
  const modulationTypeOptions = reactive([])

  const getTRAnswerFun = async (fun, data) => {
    await fun(data)
      .then(res => {
        if (res.code === 200) {
          recognizeForm.value = {
            sigFeq: round(res.data.trainResult.sigFeq / 1000_000, 2),
            sigWidth: round(res.data.trainResult.sigWidth / 1000, 2),
            sigAmp: res.data.trainResult.sigAmp,
            sigType: signalTypeOptions.find(item => item.value === res.data.trainResult.sigType)
              ?.label,
            modType: modulationTypeOptions.find(item => item.value === res.data.trainResult.modType)
              ?.label,
            isHop: res.data.trainResult.isHop === 1 ? '是' : '否',
            isSpr: res.data.trainResult.isSpr === 1 ? '是' : '否'
          }
          standardForm.value = {
            sigFeq: round(res.data.signalRecognitionAnswer.sigFeq / 1000_000, 2),
            sigWidth: round(res.data.signalRecognitionAnswer.sigWidth / 1000, 2),
            sigAmp: res.data.signalRecognitionAnswer.sigAmp,
            sigType: signalTypeOptions.find(
              item => item.value === res.data.signalRecognitionAnswer.sigType
            )?.label,
            modType: modulationTypeOptions.find(
              item => item.value === res.data.signalRecognitionAnswer.modType
            )?.label,
            isHop: res.data.signalRecognitionAnswer.isHop === 1 ? '是' : '否',
            isSpr: res.data.signalRecognitionAnswer.isSpr === 1 ? '是' : '否'
          }
          operationRecord.value = dealSteps(res.data.trainOpratorts)
          trainInfo.value = res.data.train
          rltscore.value = res.data.train.rltscore
          opscore.value = res.data.train.opscore
        }
      })
      .catch(err => {
        console.log(err)
      })
  }

  const saveScoreFun = async () => {
    const params = {
      ...trainInfo.value,
      opscore: Number(opscore.value),
      rltscore: Number(rltscore.value),
      score: Number(opscore.value) + Number(rltscore.value)
    }
    await teaScore(params).then(res => {
      if (res.code === 200) {
        ElMessage.success(res.msg)
        returnBack()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }

  const returnBack = () => {
    router.push({ name: 'TrainingRecord' })
  }

  const getDictionaryData = async () => {
    dictStore.dict
      .filter(item => item.dictType === 'operation_steps')
      .forEach(item => {
        operationStepsOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
    dictStore.dict
      .filter(item => item.dictType === 'signal_type')
      .forEach(item => {
        signalTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
    dictStore.dict
      .filter(item => item.dictType === 'modulation_type')
      .forEach(item => {
        modulationTypeOptions.push({ label: item.dictLabel, value: item.dictValue })
      })
  }

  const dealSteps = steps => {
    let result = '' // 使用 let 声明 result，因为需要重新赋值
    steps.forEach((step, index) => {
      if (step.opratName !== undefined) {
        result += `${index + 1}、${getStepLabel(step.opratName)}\n`
      }
    })
    return result
  }

  // 辅助方法：根据操作步骤的值获取对应标签
  const getStepLabel = opratName => {
    const found = operationStepsOptions.find(item => item.value === opratName.toString())
    return found ? found.label : opratName.toString()
  }

  onBeforeMount(() => {
    // 检查路由参数
    const { type, data } = route.query
    if (type === 'check') {
      getTRAnswerFun(checkTRAnswer, data)
    } else {
      getTRAnswerFun(getTRAnswer, data)
    }
    viewType.value = type || 'check'
  })

  onMounted(async () => {
    getDictionaryData()
  })
</script>

<template>
  <!-- 识别结果 -->
  <el-row class="m-5">
    <el-col :span="14" class="px-10">
      <el-row :gutter="100">
        <el-col :span="13">
          <div class="text-center text-xl font-bold mb-5">识别结果</div>
          <el-form disabled :model="recognizeForm" label-width="auto" label-position="left">
            <el-form-item label="信号频率：">
              <el-input v-model="recognizeForm.sigFeq">
                <template #append> MHz </template>
              </el-input>
            </el-form-item>
            <el-form-item label="信号带宽：">
              <el-input v-model="recognizeForm.sigWidth">
                <template #append> kHz </template>
              </el-input>
            </el-form-item>
            <el-form-item label="信号功率：">
              <el-input v-model="recognizeForm.sigAmp">
                <template #append> dBm </template>
              </el-input>
            </el-form-item>
            <el-form-item label="信号类型：">
              <el-input v-model="recognizeForm.sigType"> </el-input>
            </el-form-item>
            <el-form-item label="调制样式：">
              <el-input v-model="recognizeForm.modType"> </el-input>
            </el-form-item>
            <el-form-item label="是否跳频：">
              <el-input v-model="recognizeForm.isHop"> </el-input>
            </el-form-item>
            <el-form-item label="是否扩频：">
              <el-input v-model="recognizeForm.isSpr"> </el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="11">
          <div class="text-center text-xl font-bold mb-5">标准答案</div>
          <el-form disabled :model="standardForm" label-width="auto" label-position="left">
            <el-form-item>
              <el-input v-model="standardForm.sigFeq">
                <template #append> MHz </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="standardForm.sigWidth">
                <template #append> kHz </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="standardForm.sigAmp">
                <template #append> dBm </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="standardForm.sigType"> </el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="standardForm.modType"> </el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="standardForm.isHop"> </el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="standardForm.isSpr"> </el-input>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row class="mt-5">
        <div class="text-xl font-bold mb-8">操作记录</div>
        <el-input :disabled="true" v-model="operationRecord" type="textarea" :rows="6"></el-input>
      </el-row>
    </el-col>
    <el-col :span="10">
      <div class="text-center text-xl font-bold mb-10">识别结果得分</div>
      <div
        class="w-60 m-auto h-60 text-center leading-[15rem] rounded-full font-bold text-7xl text-white bg-[#4aa4ea]"
        >{{ rltscore }}
      </div>
      <div class="text-center text-xl font-bold mt-16 mb-5">操作过程得分</div>
      <div
        class="w-60 m-auto h-60 text-center leading-[15rem] rounded-full font-bold text-7xl text-white bg-[#4aa4ea]"
      >
        {{ opscore }}
      </div>
    </el-col>
  </el-row>
  <!-- 功能按钮 -->
  <div class="flex items-center justify-center gap-40 mt-20">
    <cu-button
      v-if="viewType === 'edit'"
      content="调整得分"
      @click="adjustScoreFlag = true"
    ></cu-button>
    <cu-button v-if="viewType === 'edit'" content="保存得分" @click="saveScoreFun"></cu-button>
  </div>

  <cu-dialog
    width="450"
    title="调整得分"
    v-model="adjustScoreFlag"
    @confirm="adjustScoreFlag = false"
    @cancel="adjustScoreFlag = false"
  >
    <el-form :model="recognizeForm" label-width="auto" label-position="left">
      <el-form-item label="识别结果得分：">
        <el-input v-model="rltscore" type="Number" min="0"> </el-input>
      </el-form-item>
      <el-form-item label="操作过程得分：">
        <el-input v-model="opscore" type="Number" min="0"> </el-input>
      </el-form-item>
    </el-form>
  </cu-dialog>
</template>

<style lang="scss" scoped></style>
