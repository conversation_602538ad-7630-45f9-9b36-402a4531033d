<script setup>
  import { ref, reactive, getCurrentInstance } from 'vue'
  import * as mtApi from '@/api/business/teacher'
  import mtDetailInfo from './tmpInfo.vue'

  // 获取组件实例，便于调用全局方法（例如 $modal、$model、download 等）
  const { proxy } = getCurrentInstance()

  // 定义子组件的 ref
  const refsTableForm = ref(null)
  const refsMt = ref(null)
  const refsImport = ref(null)

  // 定义选中行（注意：Vue3 中 ref 需要通过 .value 访问）
  const selection = ref([])

  // 定义表单配置（reactive 会使对象响应式）
  const tableFormConfig = reactive({
    title: '教师管理',
    searchConfig: {
      labelWidth: '6em',
      formConfig: [
        { title: '教师姓名', name: 'teaName' },
        { title: '手机号码', name: 'teaPhone' },
        {
          title: '入职时间',
          type: 'rangNum',
          name: 'teaIndate',
          min: 1949,
          max: new Date().getFullYear(),
          controls: false,
          placeholder: '开始年份,结束年份',
          childName: ['start', 'end']
        }
      ],
      loadData: p => {
        // 这里可根据实际需求处理参数 p
        p.start = p.start
        p.end = p.end
        return mtApi.listTeacher(p)
      }
    },
    actions: [
      {
        label: '新增',
        type: 'primary',
        icon: 'el_Plus',
        permission: ['business:teacher:add'],
        click: () => {
          refsMt.value.show()
        }
      },
      {
        label: '导出',
        icon: 'el_upload',
        type: 'warning',
        permission: ['business:teacher:export'],
        click: row =>
          // 这里假设 download 是全局方法，如不行可自行实现或引入
          proxy.download('business/teacher/export', {}, `教师管理_${new Date().getTime()}.xlsx`)
      },
      {
        type: 'danger',
        label: '删除',
        permission: ['business:teacher:remove'],
        disabled: () => !selection.value.length,
        icon: 'el_Delete',
        click: () => deleteAll()
      },
      {
        label: '导入',
        type: 'info',
        icon: 'el_download',
        click: () => {
          importFile()
        }
      }
    ],
    tableConfig: {
      isChecked: true,
      cols: [
        { label: '教师编号', prop: 'teaCode', width: 180 },
        { label: '教师姓名', prop: 'teaName', minWidth: 200 },
        {
          label: '教师性别',
          prop: 'teaSex',
          type: 'dict',
          width: 100,
          dictName: 'sys_user_sex'
        },
        { label: '手机号码', prop: 'teaPhone' },
        { label: '入职时间', prop: 'teaIndate' },
        { label: '专业', prop: 'teaMajor', type: 'dict', dictName: 'stu_major' },
        { label: '学历', prop: 'teaEducation', type: 'dict', dictName: 'teaching_education' },
        {
          label: '操作',
          type: 'action',
          width: '80',
          actions: [
            {
              title: '编辑',
              icon: 'el_Edit',
              permission: ['business:teacher:edit'],
              click: row => refsMt.value.show(row.teaId)
            },
            {
              title: '删除',
              icon: 'el_Delete',
              permission: ['business:teacher:remove'],
              click: row => deleteAll(row.teaId)
            }
          ]
        }
      ]
    }
  })

  // 获取列表数据方法
  function getList(data = null) {
    refsTableForm.value.loadData(data)
  }

  // 批量删除方法
  function deleteAll(id = null) {
    if (!selection.value.length && !id) {
      return proxy.$model.msgWarning('请选择要操作的数据')
    }
    proxy.$modal.confirm('确认删除所选教师吗？', '提示').then(() => {
      const ids = id ? [id] : selection.value.map(v => v.teaId)
      mtApi.delTeacher(ids).then(res => {
        proxy.$modal.msgSuccess(res.msg)
        getList()
      })
    })
  }

  // 导入文件方法
  function importFile() {
    refsImport.value.show({
      tmpUrl: '/business/teacher/excelTemplate',
      tmpName: '教师管理',
      uploadUrl: '/business/teacher/importData',
      ok: res => {
        getList()
      }
    })
  }
</script>

<template>
  <div class="app-container">
    <table-form
      v-bind="tableFormConfig"
      ref="refsTableForm"
      @selectionChange="rows => (selection.value = rows)"
    />
    <mtDetailInfo ref="refsMt" @actSuccess="getList()" />
    <c-import ref="refsImport" />
  </div>
</template>
